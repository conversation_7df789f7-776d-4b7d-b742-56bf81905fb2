# Cloudflare Workers 快速参考

## 🚀 常用命令速查

### 开发阶段

```bash
# 本地开发
pnpm dev                          # Next.js + Cloudflare适配（推荐）
pnpm exec wrangler dev            # 纯Cloudflare环境

# 认证
npx wrangler auth login           # 登录
npx wrangler auth whoami          # 查看当前用户
```

### 实时日志

```bash
# 基础日志查看
pnpm exec wrangler tail           # 推荐：使用项目内wrangler
npx wrangler tail                 # 使用全局wrangler

# 过滤日志
npx wrangler tail --status error  # 只看错误
npx wrangler tail --method POST   # 过滤HTTP方法
npx wrangler tail --search "关键词" # 搜索内容
npx wrangler tail --format pretty # 美化输出

# 日志分析（需要安装jq）
npx wrangler tail | jq .event.request.url    # 显示请求URL
npx wrangler tail | jq '.logs[].message'     # 显示console.log消息
```

### 部署命令

```bash
# 项目部署
npm run cf:preview                # 预览部署（测试）
npm run cf:deploy                 # 生产部署

# 原生wrangler部署
npx wrangler deploy               # 直接部署
npx wrangler deploy --dry-run     # 模拟部署
npx wrangler deploy --env staging # 部署到staging环境
```

### 版本管理

```bash
npx wrangler deployments list    # 查看部署历史
npx wrangler rollback            # 回滚到上一版本
npx wrangler status              # 查看当前状态
```

## 🔧 故障排查

### 网络问题

```bash
# 设置代理（如果需要）
export https_proxy=http://proxy:port
export http_proxy=http://proxy:port

# 测试连接
ping api.cloudflare.com
curl -I https://api.cloudflare.com
```

### 部署问题

```bash
# 详细调试信息
WRANGLER_LOG=debug npm run cf:deploy

# 检查构建产物
ls -la .open-next/
du -sh .open-next/worker.js

# 分析构建大小
npm run analyze
```

### 运行时错误

```bash
# 查看错误详情
npx wrangler tail --status error --format pretty

# 查看异常堆栈
npx wrangler tail | jq 'select(.exceptions | length > 0)'
```

## 📊 监控运维

### 基础监控

```bash
# 查看性能指标
npx wrangler metrics

# 健康检查
curl -f https://your-worker.workers.dev/health

# 持续监控错误
npx wrangler tail --status error > error.log &
```

### 日志管理

```bash
# 保存日志到文件
npx wrangler tail > logs/worker-$(date +%Y%m%d).log

# 后台运行日志收集
nohup npx wrangler tail > logs.txt 2>&1 &
```

## 🛠️ 开发技巧

### 代码中的日志记录

```typescript
import logger from '@/lib/logger';

// 结构化日志（推荐）
logger.info('Request processed', { userId, action });
logger.error('Database error', error, { query });

// 简单日志
console.log('Request:', req.url);
console.error('Error:', error.message);
```

### 环境配置

```bash
# 复制配置文件
cp wrangler.toml.example wrangler.toml
cp .env.example .env.production

# 设置密钥
npx wrangler secret put DATABASE_URL
npx wrangler secret list
```

## 🔄 命令对比

| 场景 | 命令选择 | 说明 |
|------|----------|------|
| 本地开发 | `pnpm dev` | Next.js开发，支持热重载 |
| Workers测试 | `wrangler dev` | 纯Cloudflare环境 |
| 查看日志 | `pnpm exec wrangler tail` | 使用项目内版本 |
| 生产部署 | `npm run cf:deploy` | 完整构建+部署流程 |
| 快速部署 | `npx wrangler deploy` | 原生wrangler部署 |

## 📱 Denoflare 替代方案

```bash
# 安装
npm install -g denoflare

# 使用
denoflare tail worker-name --format pretty
denoflare tail worker-name --status error
```

---

> 💡 **提示**:
>
> - 优先使用 `pnpm exec wrangler` 确保版本一致性
> - 生产环境使用 `npm run cf:deploy` 确保完整构建
> - 网络问题时设置代理或使用 Dashboard 查看日志
> - 定期备份重要日志和配置文件
