# Cloudflare Workers 开发运维指南

本文档提供了基于本项目的 Cloudflare Workers 应用完整的开发、调试、部署和运维指南。

## 📖 目录

- [环境配置](#environment-setup)
- [开发流程](#development-workflow)
- [调试和日志](#debugging-and-logging)
- [部署策略](#deployment-strategy)
- [运维监控](#operations-monitoring)
- [故障排查](#troubleshooting)
- [性能优化](#performance-optimization)
- [常用命令参考](#command-reference)

## 🔧 环境配置 {#environment-setup}

### 1. 基础环境

```bash
# 确保使用正确的Node.js版本
node --version  # >= 18.0.0

# 安装项目依赖
pnpm install

# 验证wrangler版本
pnpm exec wrangler --version
```

### 2. 认证配置

```bash
# 登录Cloudflare账户
npx wrangler auth login

# 验证登录状态
npx wrangler auth whoami

# 查看可用的账户
npx wrangler auth list
```

### 3. 环境变量配置

```bash
# 复制配置文件
cp .env.example .env.production
cp wrangler.toml.example wrangler.toml

# 编辑配置文件
vim wrangler.toml
```

**wrangler.toml 配置示例：**

```toml
name = "your-worker-name"
main = ".open-next/worker.js"
compatibility_date = "2025-03-01"
compatibility_flags = ["nodejs_compat", "global_fetch_strictly_public"]

[assets]
binding = "ASSETS"
directory = ".open-next/assets"

[observability]
enabled = true

[vars]
DATABASE_URL = "your-database-url"
NEXTAUTH_SECRET = "your-secret"
# 添加其他环境变量
```

## 🚀 开发流程 {#development-workflow}

### 1. 本地开发

```bash
# 启动本地开发服务器（推荐）
pnpm dev

# 使用Cloudflare开发环境
pnpm exec wrangler dev

# 指定端口和协议
pnpm exec wrangler dev --port 3000 --local-protocol https
```

**命令对比：**

| 命令 | 描述 | 适用场景 |
|------|------|----------|
| `pnpm dev` | Next.js开发服务器 + Cloudflare适配 | 日常开发，热重载 |
| `wrangler dev` | 纯Cloudflare环境模拟 | 测试Workers特性 |

### 2. 代码开发最佳实践

#### 日志记录

```typescript
import logger from '@/lib/logger';

export async function POST(req: Request) {
  // 结构化日志
  logger.info('API request received', { 
    method: req.method,
    url: req.url 
  });

  try {
    const result = await processRequest(req);
    logger.info('Request processed successfully', { result });
    return Response.json(result);
  } catch (error) {
    logger.error('Request failed', error, { 
      requestId: crypto.randomUUID() 
    });
    throw error;
  }
}
```

#### 错误处理

```typescript
// 全局错误处理
export default {
  async fetch(request: Request, env: Env): Promise<Response> {
    try {
      return await handleRequest(request, env);
    } catch (error) {
      console.error('Unhandled error:', error);
      return new Response('Internal Server Error', { status: 500 });
    }
  },
};
```

## 🐛 调试和日志 {#debugging-and-logging}

### 1. 实时日志查看

#### Wrangler Tail

```bash
# 基础用法
npx wrangler tail

# 项目内wrangler（推荐）
pnpm exec wrangler tail

# 带过滤的日志
npx wrangler tail --format pretty

# 过滤特定状态
npx wrangler tail --status error

# 过滤HTTP方法
npx wrangler tail --method POST

# 搜索特定内容
npx wrangler tail --search "database"

# 采样率设置（减少高流量时的日志量）
npx wrangler tail --sampling-rate 0.1
```

**命令对比：**

| 命令 | 描述 | 优势 |
|------|------|------|
| `npx wrangler tail` | 使用全局或npx安装的wrangler | 简单快速 |
| `pnpm exec wrangler tail` | 使用项目内的wrangler版本 | 版本一致性 |

#### Denoflare 替代方案

```bash
# 安装denoflare
npm install -g denoflare

# 使用denoflare tail
denoflare tail your-worker-name

# 带格式化输出
denoflare tail your-worker-name --format pretty

# 带过滤
denoflare tail your-worker-name --status error --method POST
```

### 2. 日志分析

#### 使用jq分析JSON日志

```bash
# 只显示错误日志
npx wrangler tail | jq 'select(.outcome == "exception")'

# 显示请求URL
npx wrangler tail | jq .event.request.url

# 显示响应状态码
npx wrangler tail | jq .event.response.status

# 显示console.log消息
npx wrangler tail | jq '.logs[].message'

# 统计错误类型
npx wrangler tail | jq -r 'select(.exceptions | length > 0) | .exceptions[].name' | sort | uniq -c
```

#### 日志持久化

```bash
# 保存日志到文件
npx wrangler tail > worker-logs.json

# 后台运行日志收集
nohup npx wrangler tail > logs/$(date +%Y%m%d_%H%M%S).log 2>&1 &

# 按日期滚动日志
npx wrangler tail | tee logs/worker-$(date +%Y%m%d).log
```

### 3. 调试技巧

#### 本地调试

```bash
# 启用详细调试信息
WRANGLER_LOG=debug npx wrangler dev

# 使用Node.js调试器
node --inspect-brk ./node_modules/.bin/wrangler dev

# 使用VS Code调试
# 在.vscode/launch.json中配置
```

#### 远程调试

```bash
# 部署到preview环境
npx wrangler deploy --env preview

# 查看preview环境日志
npx wrangler tail --env preview
```

## 🚀 部署策略 {#deployment-strategy}

### 1. 部署命令对比

| 命令 | 描述 | 用途 |
|------|------|------|
| `npx wrangler deploy` | 原生wrangler部署 | 简单Workers项目 |
| `npm run cf:deploy` | 项目自定义部署脚本 | Next.js + OpenNext构建部署 |
| `npm run cf:preview` | 预览部署 | 测试环境验证 |
| `npm run cf:upload` | 仅上传，不部署 | CI/CD环境 |

### 2. 部署流程

#### 开发环境部署

```bash
# 预览部署（不影响生产）
npm run cf:preview

# 查看预览链接
npx wrangler deployments list

# 预览特定版本
npx wrangler deploy --compatibility-date 2025-01-01
```

#### 生产环境部署

```bash
# 完整部署流程
npm run cf:deploy

# 等价于以下步骤：
# 1. opennextjs-cloudflare build
# 2. opennextjs-cloudflare deploy

# 手动步骤部署
npx opennextjs-cloudflare build
npx opennextjs-cloudflare deploy
```

#### 版本管理

```bash
# 查看部署历史
npx wrangler deployments list

# 回滚到指定版本
npx wrangler rollback [deployment-id]

# 查看当前版本信息
npx wrangler status
```

### 3. 环境管理

#### 多环境配置

```bash
# 部署到staging环境
npx wrangler deploy --env staging

# 部署到production环境
npx wrangler deploy --env production

# 查看环境列表
npx wrangler env list
```

**wrangler.toml 多环境配置：**

```toml
name = "my-worker"

[env.staging]
name = "my-worker-staging"
vars = { ENVIRONMENT = "staging" }

[env.production]
name = "my-worker-production"
vars = { ENVIRONMENT = "production" }
```

## 📊 运维监控 {#operations-monitoring}

### 1. 监控指标

#### 性能监控

```bash
# 查看Workers分析数据
npx wrangler metrics

# 获取特定时间段的指标
npx wrangler metrics --since 2025-01-01 --until 2025-01-02
```

#### 日志监控

```bash
# 监控错误日志
npx wrangler tail --status error | while read line; do
  echo "$(date): $line" >> error.log
  # 可以添加告警逻辑
done

# 监控响应时间
npx wrangler tail | jq '.event.response.status' | while read status; do
  if [ "$status" -gt 499 ]; then
    echo "Server error detected: $status"
  fi
done
```

### 2. 告警设置

#### 自定义告警脚本

```bash
#!/bin/bash
# monitor.sh - 监控脚本

ERROR_THRESHOLD=10
ERROR_COUNT=0

npx wrangler tail --status error | while read line; do
  ERROR_COUNT=$((ERROR_COUNT + 1))
  
  if [ $ERROR_COUNT -gt $ERROR_THRESHOLD ]; then
    # 发送告警（示例：发送到Slack/邮件/钉钉）
    curl -X POST -H 'Content-type: application/json' \
      --data '{"text":"Worker error threshold exceeded"}' \
      YOUR_WEBHOOK_URL
    
    ERROR_COUNT=0
    sleep 60  # 防止告警风暴
  fi
done
```

### 3. 健康检查

```bash
# 检查Worker状态
curl -f https://your-worker.workers.dev/health || echo "Worker is down"

# 检查特定端点
curl -o /dev/null -s -w "%{http_code}\n" https://your-worker.workers.dev/api/status
```

## 🛠️ 故障排查 {#troubleshooting}

### 1. 常见问题诊断

#### 网络连接问题

```bash
# 测试到Cloudflare的连接
ping api.cloudflare.com

# 检查DNS解析
nslookup api.cloudflare.com

# 测试HTTPS连接
curl -I https://api.cloudflare.com

# 使用代理
export https_proxy=http://proxy:port
npx wrangler tail
```

#### 部署失败诊断

```bash
# 详细部署日志
WRANGLER_LOG=debug npm run cf:deploy

# 检查构建产物
ls -la .open-next/

# 验证配置
npx wrangler config

# 检查文件大小
du -sh .open-next/worker.js
```

#### 运行时错误诊断

```bash
# 查看错误详情
npx wrangler tail --status error --format pretty

# 检查特定错误
npx wrangler tail | jq 'select(.exceptions | length > 0)'

# 查看堆栈跟踪
npx wrangler tail | jq '.exceptions[].stack'
```

### 2. 性能问题排查

#### 响应时间分析

```bash
# 监控响应时间
npx wrangler tail | jq '.event | {url: .request.url, duration: .response.duration}'

# 检查缓存命中率
npx wrangler tail | jq '.event.cf.cacheStatus'
```

#### 内存使用监控

```bash
# 在代码中添加内存监控
console.log('Memory usage:', process.memoryUsage());

# 查看内存相关日志
npx wrangler tail | grep -i memory
```

## ⚡ 性能优化 {#performance-optimization}

### 1. 构建优化

```bash
# 分析构建产物大小
npm run analyze

# 优化构建配置
# 在 next.config.mjs 中启用优化选项
```

### 2. 运行时优化

#### 缓存策略

```typescript
// 在Worker中实现缓存
const cache = caches.default;

export default {
  async fetch(request: Request): Promise<Response> {
    const cacheKey = new Request(request.url, request);
    const cached = await cache.match(cacheKey);
    
    if (cached) {
      return cached;
    }
    
    const response = await handleRequest(request);
    
    // 缓存响应
    if (response.status === 200) {
      const responseToCache = response.clone();
      responseToCache.headers.set('Cache-Control', 'max-age=3600');
      await cache.put(cacheKey, responseToCache);
    }
    
    return response;
  },
};
```

#### 连接池优化

```typescript
// 数据库连接优化（参考 src/db/index.ts）
const client = postgres(databaseUrl, {
  max: 1, // Workers环境限制连接数
  idle_timeout: 10,
  connect_timeout: 5,
});
```

## 📋 常用命令参考 {#command-reference}

### Wrangler 核心命令

```bash
# 认证相关
npx wrangler auth login          # 登录
npx wrangler auth logout         # 登出
npx wrangler auth whoami         # 查看当前用户

# 开发调试
npx wrangler dev                 # 本地开发
npx wrangler dev --local         # 完全本地模式
npx wrangler dev --remote        # 远程模式

# 部署管理
npx wrangler deploy              # 部署到生产
npx wrangler deploy --dry-run    # 模拟部署
npx wrangler deploy --env staging # 部署到staging

# 日志查看
npx wrangler tail                # 实时日志
npx wrangler tail --format pretty # 格式化输出
npx wrangler tail --status error # 只看错误
npx wrangler tail --search "text" # 搜索内容

# 版本管理
npx wrangler deployments list   # 查看部署历史
npx wrangler rollback           # 回滚
npx wrangler status             # 查看状态

# 配置管理
npx wrangler config             # 查看配置
npx wrangler env list           # 查看环境
npx wrangler secret put KEY     # 设置密钥
npx wrangler secret list        # 查看密钥

# 其他工具
npx wrangler types              # 生成类型定义
npx wrangler metrics            # 查看指标
npx wrangler pages project list # 查看Pages项目
```

### 项目特定命令

```bash
# 开发命令
pnpm dev                        # Next.js开发服务器
pnpm exec wrangler dev          # Cloudflare开发环境

# 构建命令
pnpm build                      # Next.js构建
npm run analyze                 # 构建分析

# 部署命令
npm run cf:preview              # 预览部署
npm run cf:deploy               # 生产部署
npm run cf:upload               # 仅上传

# 数据库命令
npm run db:generate             # 生成迁移
npm run db:migrate              # 执行迁移
npm run db:studio               # 数据库管理界面

# 类型生成
npm run cf:typegen              # 生成Cloudflare类型
```

### Denoflare 命令

```bash
# 基础命令
denoflare tail worker-name               # 查看日志
denoflare tail worker-name --format pretty # 格式化输出
denoflare tail worker-name --once          # 只看一条日志

# 过滤选项
denoflare tail worker-name --status error     # 过滤状态
denoflare tail worker-name --method POST      # 过滤方法
denoflare tail worker-name --search "keyword" # 搜索关键词
denoflare tail worker-name --sampling-rate 0.1 # 采样率

# 部署命令
denoflare push worker-name script.js    # 部署Worker
denoflare serve script.js               # 本地服务
```

### 网络调试命令

```bash
# 连接测试
ping api.cloudflare.com
curl -I https://api.cloudflare.com
traceroute api.cloudflare.com

# 代理设置
export https_proxy=http://proxy:port
export http_proxy=http://proxy:port

# WSL网络重置
wsl --shutdown                  # Windows PowerShell中执行
netsh winsock reset            # Windows PowerShell中执行
```

## 🔗 相关资源

- [Cloudflare Workers文档](https://developers.cloudflare.com/workers/)
- [Wrangler CLI文档](https://developers.cloudflare.com/workers/wrangler/)
- [OpenNext Cloudflare文档](https://opennext.js.org/cloudflare)
- [项目架构文档](./architecture.md)
- [性能优化指南](./performance-optimization.md)

---

> 💡 **提示**: 建议将此文档加入到项目书签中，便于快速查询命令和排查问题。定期更新文档内容以反映最新的最佳实践。
