import { getTranslations, setRequestLocale } from 'next-intl/server'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Link } from '@/i18n/navigation'
import Image from 'next/image'
import { Metadata } from 'next'
import { getContentList, type ProductContent, type SupportedLocale } from '@/services/content'

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params
  setRequestLocale(locale)

  const t = await getTranslations()

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/products`

  if (locale !== 'en') {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/products`
  }

  return {
    title: t('products.title') || 'Products',
    description: t('products.description') || 'Explore our products',
    alternates: {
      canonical: canonicalUrl,
    },
  }
}

export default async function ProductsPage({
  params,
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params
  setRequestLocale(locale)

  const t = await getTranslations()

  // Get products using new content service
  const products = await getContentList<ProductContent>('product', locale as SupportedLocale, {
    sortBy: 'date',
    sortOrder: 'desc'
  })

  return (
    <section className="py-16">
      <div className="container">
        <div className="mb-12 text-center">
          <h1 className="mb-4 text-4xl font-bold tracking-tight">
            {t('products.title') || 'Products'}
          </h1>
          <p className="text-xl text-muted-foreground">
            {t('products.description') || 'Explore our products'}
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {products.map((product) => (
            <Link key={product.slug} href={`/products/${product.slug}`}>
              <Card className="h-full transition-colors hover:bg-muted/50">
                {product.coverImage && (
                  <div className="aspect-video overflow-hidden rounded-t-lg">
                    <Image
                      src={product.coverImage}
                      alt={product.title}
                      width={400}
                      height={225}
                      className="h-full w-full object-cover"
                    />
                  </div>
                )}
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="line-clamp-2">{product.title}</CardTitle>
                    {product.featured && (
                      <Badge variant="secondary">Featured</Badge>
                    )}
                  </div>
                  {product.description && (
                    <CardDescription className="line-clamp-3">
                      {product.description}
                    </CardDescription>
                  )}
                </CardHeader>
                <CardContent>
                  {product.tags && product.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {product.tags.map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {products.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              {t('products.no_products') || 'No products found.'}
            </p>
          </div>
        )}
      </div>
    </section>
  )
}
