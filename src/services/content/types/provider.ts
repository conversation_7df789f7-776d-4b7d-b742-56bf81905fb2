/**
 * Content Provider Interface
 * 
 * This module defines the contract that all CMS providers must implement
 * to ensure consistent functionality across different CMS backends.
 * The provider interface abstracts away the specific implementation
 * details of different CMS systems while providing a unified API.
 * 
 * The interface supports various CMS operations including content
 * retrieval, static generation, metadata access, and multilingual
 * content management. This abstraction enables seamless switching
 * between different CMS providers without changing application code.
 * 
 * Key Features:
 * - Provider-agnostic content operations
 * - Support for static site generation
 * - Multilingual content handling
 * - Metadata and utility operations
 * - Optional advanced features (related content)
 * - Consistent error handling patterns
 * 
 * <AUTHOR> Service Team
 * @since 2.0.0 (Unified Architecture)
 */

import type {
  ContentItemUnion,
  ContentType,
  ContentMetadata,
  LanguageVersion,
  QueryOptions
} from './content'

/**
 * Content Provider Interface
 * 
 * Defines the contract that all CMS providers must implement.
 * This interface ensures that different CMS backends can be
 * used interchangeably while providing consistent functionality.
 * 
 * The provider interface is designed to support both dynamic
 * content delivery and static site generation workflows.
 * It includes methods for content retrieval, metadata access,
 * and utility operations that are common across CMS systems.
 * 
 * Providers should implement all required methods and may
 * optionally implement advanced features like related content
 * suggestions based on their capabilities.
 * 
 * @example
 * ```typescript
 * class MyCustomProvider implements ContentProvider {
 *   readonly name = 'my-custom-cms'
 *   readonly version = '1.0.0'
 *   
 *   async getContent<T extends ContentItemUnion>(
 *     type: ContentType,
 *     slug: string,
 *     locale: string
 *   ): Promise<T | null> {
 *     // Implementation specific to your CMS
 *   }
 *   
 *   // ... implement other required methods
 * }
 * ```
 */
export interface ContentProvider {
  // Provider identification
  /** Human-readable name of the CMS provider */
  readonly name: string
  /** Version of the provider implementation */
  readonly version: string
  
  // Core content operations
  /**
   * Get a single content item by type, slug, and locale
   * 
   * @param type - Content type identifier
   * @param slug - Content slug
   * @param locale - Language/locale code
   * @returns Promise resolving to content item or null if not found
   */
  getContent<T extends ContentItemUnion>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null>
  
  /**
   * Get a list of content items with optional filtering and sorting
   * 
   * @param type - Content type identifier
   * @param locale - Language/locale code
   * @param options - Query options for filtering and sorting
   * @returns Promise resolving to array of content items
   */
  getContentList<T extends ContentItemUnion>(
    type: ContentType,
    locale: string,
    options?: QueryOptions
  ): Promise<T[]>
  
  // Utility operations
  /**
   * Check if content exists without loading the full content
   * 
   * @param type - Content type identifier
   * @param slug - Content slug
   * @param locale - Language/locale code
   * @returns Promise resolving to boolean indicating existence
   */
  contentExists(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<boolean>
  
  /**
   * Get content title without loading full content (for performance)
   * 
   * @param type - Content type identifier
   * @param slug - Content slug
   * @param locale - Language/locale code
   * @returns Promise resolving to title string or null if not found
   */
  getContentTitle(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<string | null>
  
  /**
   * Get lightweight metadata for content item
   * 
   * @param type - Content type identifier
   * @param slug - Content slug
   * @param locale - Language/locale code
   * @returns Promise resolving to content metadata or null if not found
   */
  getContentMetadata(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<ContentMetadata | null>
  
  // Language and internationalization
  /**
   * Get available language versions of content
   * 
   * @param type - Content type identifier
   * @param slug - Content slug
   * @returns Promise resolving to array of language versions
   */
  getAvailableLanguages(
    type: ContentType,
    slug: string
  ): Promise<LanguageVersion[]>
  
  // Static generation support
  /**
   * Get all content for static generation (build time)
   * 
   * @param type - Content type identifier
   * @returns Promise resolving to all content items for static generation
   */
  getContentForStaticGeneration<T extends ContentItemUnion>(
    type: ContentType
  ): Promise<T[]>
  
  /**
   * Get all content slugs for static parameter generation
   * 
   * @param type - Content type identifier
   * @returns Promise resolving to array of slug/locale combinations
   */
  getAllContentSlugs(
    type: ContentType
  ): Promise<Array<{ locale: string; slug: string }>>
  
  // Optional advanced features
  /**
   * Get related content items (optional feature)
   * 
   * @param type - Content type identifier
   * @param slug - Current content slug
   * @param locale - Language/locale code
   * @param limit - Maximum number of related items to return
   * @returns Promise resolving to array of related content items
   */
  getRelatedContent?<T extends ContentItemUnion>(
    type: ContentType,
    slug: string,
    locale: string,
    limit?: number
  ): Promise<T[]>
}

/**
 * Provider Factory Function Type
 * 
 * Defines the signature for provider factory functions that
 * create and configure CMS provider instances.
 * 
 * @example
 * ```typescript
 * const createContentlayer2Provider: ProviderFactory = (config) => {
 *   return new Contentlayer2Provider(config)
 * }
 * ```
 */
export type ProviderFactory = (config?: any) => ContentProvider

/**
 * Provider Registry Interface
 * 
 * Defines the structure for registering and managing
 * multiple CMS providers in the system.
 */
export interface ProviderRegistry {
  /** Register a new provider */
  register(name: string, factory: ProviderFactory): void
  /** Get a provider by name */
  get(name: string): ProviderFactory | undefined
  /** List all registered provider names */
  list(): string[]
  /** Check if a provider is registered */
  has(name: string): boolean
}
