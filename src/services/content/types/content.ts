/**
 * Unified Content Type Definitions
 *
 * This module provides comprehensive type definitions for content items
 * in the unified content service layer. These types are designed to be
 * provider-agnostic, allowing for seamless switching between different
 * CMS backends while maintaining type safety and consistent API interfaces.
 *
 * The content types support various media formats, multilingual content,
 * and rich metadata for SEO and content management purposes.
 *
 * Key Features:
 * - Provider-agnostic content interfaces
 * - Support for MDX content with code compilation
 * - Multilingual content support with both 'lang' and 'locale' compatibility
 * - Rich media integration (images, videos)
 * - SEO-friendly metadata structure
 * - Extensible content type system
 * - Business-specific properties for different content types
 *
 * <AUTHOR> Service Team
 * @since 2.0.0 (Unified Architecture)
 */

/**
 * Content Type Identifiers
 *
 * Defines the available content types in the system.
 * These identifiers are used throughout the content service
 * layer to specify which type of content to operate on.
 *
 * Each content type can have its own specific properties
 * while sharing the common base structure defined in
 * BaseContentItem.
 *
 * @example
 * ```typescript
 * const contentType: ContentType = 'blog'
 * const products = await getContentList('product', 'en')
 * ```
 */
export type ContentType = 'blog' | 'product' | 'case-study'

/**
 * MDX Content Type
 *
 * Provider-agnostic MDX content type that abstracts away
 * the specific implementation details of different CMS providers.
 * This allows for consistent MDX handling regardless of the backend.
 *
 * The MDX content includes both the raw markdown source and
 * the compiled JavaScript code for rendering, enabling efficient
 * server-side rendering and client-side hydration.
 *
 * @example
 * ```typescript
 * const mdxContent: MDXContent = {
 *   raw: '# Hello World\n\nThis is **bold** text.',
 *   code: 'function MDXContent() { return React.createElement("h1", null, "Hello World"); }'
 * }
 * ```
 */
export interface MDXContent {
  /** Raw markdown/MDX source content */
  raw: string
  /** Compiled JavaScript code for rendering */
  code: string
}

/**
 * Base Content Item Interface
 *
 * Defines the common structure that all content items must have
 * regardless of their specific type. This ensures consistency
 * across different content types and CMS providers.
 *
 * The base interface includes essential fields for identification,
 * content storage, media handling, publishing workflow, and SEO.
 * All specific content types extend this interface.
 *
 * Note: This interface supports both 'lang' (CMS provider format)
 * and 'locale' (application format) for maximum compatibility.
 *
 * @example
 * ```typescript
 * const baseContent: BaseContentItem = {
 *   slug: 'my-content',
 *   title: 'My Content Title',
 *   lang: 'en',
 *   locale: 'en',
 *   url: '/en/my-content',
 *   body: { raw: '# Content', code: 'compiled...' },
 *   createdAt: '2024-01-01T00:00:00Z',
 *   featured: false
 * }
 * ```
 */
export interface BaseContentItem {
  // Core identification fields
  /** Unique slug identifier for the content */
  slug: string
  /** Display title of the content */
  title: string
  /** Language code (ISO 639-1) - CMS provider format */
  lang: string
  /** Content language/locale - application format (for compatibility) */
  locale: string
  /** Full URL path to the content */
  url: string

  // Content and metadata
  /** Optional short description or excerpt */
  description?: string
  /** Main content body in MDX format */
  body: MDXContent

  // Publishing and workflow
  /** Content type identifier */
  type: ContentType
  /** Whether the content is published and visible */
  published?: boolean
  /** Publication date in human-readable format */
  date?: string
  /** Publication date in ISO 8601 format */
  publishedAt?: string
  /** Creation date in ISO 8601 format */
  createdAt?: string
  /** Last modification date in ISO 8601 format */
  updatedAt?: string

  // Content organization
  /** Content tags for categorization and filtering */
  tags?: string[]
  /** Whether this content is featured/highlighted */
  featured?: boolean
  /** Author name or identifier */
  author?: string

  // Media and visual content
  /** Featured image URL (legacy field for compatibility) */
  image?: string
  /** URL to the cover/featured image */
  coverImage?: string
  /** URL to the author's profile image */
  authorImage?: string
  /** URL to an associated video */
  videoUrl?: string
  /** URL to the video thumbnail image */
  videoThumbnail?: string
  /** Duration of the video in human-readable format */
  videoDuration?: string

  // SEO and metadata
  /** SEO-specific metadata */
  seo?: {
    title?: string
    description?: string
    keywords?: string[]
  }
}

/**
 * Content Item Union Type (for backward compatibility)
 *
 * Represents any type of content item in the system.
 * This union type allows for type-safe handling of different
 * content types while maintaining their specific properties.
 *
 * Note: This is an alias for BaseContentItem to maintain
 * compatibility with existing code that uses ContentItem.
 */
export type ContentItem = BaseContentItem

/**
 * Blog Content Interface
 *
 * Represents blog post content with blog-specific properties.
 * Blog content includes reading time estimation, category
 * classification, and rich content features.
 *
 * @extends BaseContentItem
 *
 * @example
 * ```typescript
 * const blog: BlogContent = {
 *   type: 'blog',
 *   slug: 'understanding-typescript',
 *   title: 'Understanding TypeScript',
 *   lang: 'en',
 *   locale: 'en',
 *   url: '/blog/understanding-typescript',
 *   body: { raw: '# TypeScript Guide', code: 'compiled...' },
 *   author: 'John Doe',
 *   publishedAt: '2024-01-15T10:00:00Z',
 *   createdAt: '2024-01-10T09:00:00Z',
 *   featured: true,
 *   tags: ['typescript', 'programming'],
 *   readingTime: 5,
 *   category: 'tutorial'
 * }
 * ```
 */
export interface BlogContent extends BaseContentItem {
  /** Content type identifier */
  type: 'blog'
  /** Estimated reading time in minutes */
  readingTime?: number
  /** Blog category for organization */
  category?: string
}

/**
 * Product Content Interface
 *
 * Represents product or service content with product-specific
 * properties. Product content includes business-specific fields
 * like pricing, features, and an optional icon for visual
 * representation in product listings and catalogs.
 *
 * @extends BaseContentItem
 *
 * @example
 * ```typescript
 * const product: ProductContent = {
 *   type: 'product',
 *   slug: 'awesome-widget',
 *   title: 'Awesome Widget Pro',
 *   lang: 'en',
 *   locale: 'en',
 *   url: '/products/awesome-widget',
 *   body: { raw: '# Product Description', code: 'compiled...' },
 *   icon: '/icons/widget.svg',
 *   price: '$99.99',
 *   features: ['Feature 1', 'Feature 2'],
 *   createdAt: '2024-01-01T00:00:00Z',
 *   featured: false,
 *   tags: ['widget', 'productivity']
 * }
 * ```
 */
export interface ProductContent extends BaseContentItem {
  /** Content type identifier */
  type: 'product'
  /** Optional icon URL for product representation */
  icon?: string
  /** Product price */
  price?: string
  /** Product features list */
  features?: string[]
  /** Product specifications */
  specifications?: Record<string, string>
  /** Product category */
  category?: string
  /** Product availability status */
  available?: boolean
}

/**
 * Case Study Content Interface
 *
 * Represents case study content showcasing projects,
 * implementations, or success stories. Case studies
 * include detailed project information, client details,
 * and measurable outcomes.
 *
 * @extends BaseContentItem
 *
 * @example
 * ```typescript
 * const caseStudy: CaseStudyContent = {
 *   type: 'case-study',
 *   slug: 'client-success-story',
 *   title: 'How We Improved Client Performance by 300%',
 *   lang: 'en',
 *   locale: 'en',
 *   url: '/case-studies/client-success-story',
 *   body: { raw: '# Case Study Details', code: 'compiled...' },
 *   client: 'TechCorp Inc.',
 *   industry: 'Technology',
 *   duration: '6 months',
 *   results: ['300% performance improvement', '50% cost reduction'],
 *   technologies: ['React', 'Node.js', 'PostgreSQL'],
 *   createdAt: '2024-01-01T00:00:00Z',
 *   featured: true,
 *   tags: ['performance', 'optimization']
 * }
 * ```
 */
export interface CaseStudyContent extends BaseContentItem {
  /** Content type identifier */
  type: 'case-study'
  /** Client name */
  client: string
  /** Industry sector */
  industry?: string
  /** Project duration */
  duration?: string
  /** Key results achieved */
  results?: string[]
  /** Technologies used */
  technologies?: string[]
}

/**
 * Content Item Union Type
 *
 * Represents any type of content item in the system.
 * This union type allows for type-safe handling of different
 * content types while maintaining their specific properties.
 *
 * The union type enables polymorphic content handling where
 * functions can accept any content type and use type guards
 * or discriminated union patterns to handle type-specific logic.
 *
 * @example
 * ```typescript
 * function processContent(content: ContentItemUnion) {
 *   switch (content.type) {
 *     case 'blog':
 *       // Handle blog-specific logic
 *       console.log(`Reading time: ${content.readingTime} minutes`)
 *       break
 *     case 'product':
 *       // Handle product-specific logic (access content.icon, content.price)
 *       console.log(`Product price: ${content.price}`)
 *       break
 *     case 'case-study':
 *       // Handle case study-specific logic
 *       console.log(`Client: ${content.client}`)
 *       break
 *   }
 * }
 * ```
 */
export type ContentItemUnion = BlogContent | ProductContent | CaseStudyContent

/**
 * Content Query Options Interface
 *
 * Defines the available options for querying content from
 * CMS providers. These options provide flexible filtering,
 * sorting, and pagination capabilities that work across
 * different CMS backends.
 *
 * The query options are designed to be provider-agnostic
 * while supporting common content management use cases
 * such as featured content, tag-based filtering, and
 * author-specific queries.
 *
 * @example
 * ```typescript
 * const options: QueryOptions = {
 *   featured: true,
 *   tags: ['typescript', 'tutorial'],
 *   sortBy: 'publishedAt',
 *   order: 'desc',
 *   limit: 10,
 *   offset: 0
 * }
 *
 * const content = await getContentList('blog', 'en', options)
 * ```
 */
export interface QueryOptions {
  // Filtering options
  /** Filter by featured status */
  featured?: boolean
  /** Filter by published status */
  published?: boolean
  /** Filter by specific tags */
  tags?: string[]
  /** Filter by category */
  category?: string
  /** Filter by author */
  author?: string
  /** Search query string */
  search?: string

  // Sorting options
  /** Field to sort by */
  sortBy?: 'date' | 'title' | 'slug' | 'publishedAt' | 'createdAt' | 'updatedAt'
  /** Sort order */
  sortOrder?: 'asc' | 'desc'
  /** Legacy sort order field (for compatibility) */
  order?: 'asc' | 'desc'

  // Pagination options
  /** Maximum number of items to return */
  limit?: number
  /** Number of items to skip (for pagination) */
  offset?: number
}

/**
 * Content Metadata Interface
 *
 * Represents lightweight content metadata without the full content body.
 * This is used for operations that only need basic information about
 * content items, such as listing pages or navigation components.
 *
 * @example
 * ```typescript
 * const metadata: ContentMetadata = {
 *   slug: 'my-post',
 *   title: 'My Blog Post',
 *   description: 'A great blog post',
 *   type: 'blog',
 *   lang: 'en',
 *   locale: 'en',
 *   url: '/blog/my-post',
 *   publishedAt: '2024-01-01T00:00:00Z'
 * }
 * ```
 */
export interface ContentMetadata {
  /** Content slug */
  slug: string
  /** Content title */
  title: string
  /** Content description */
  description?: string
  /** Content type */
  type: ContentType
  /** Language code (CMS format) */
  lang: string
  /** Locale (application format) */
  locale: string
  /** Content URL */
  url: string
  /** Publication date */
  publishedAt?: string
  /** Creation date */
  createdAt?: string
  /** Published status */
  published?: boolean
  /** Content tags */
  tags?: string[]
  /** Featured image URL */
  coverImage?: string
  /** Whether content is featured */
  featured?: boolean
}

/**
 * Language Version Interface
 *
 * Represents different language versions of the same content item.
 * Used for language switching and alternate language detection.
 *
 * @example
 * ```typescript
 * const versions: LanguageVersion[] = [
 *   { lang: 'en', title: 'English Title', url: '/blog/post', available: true },
 *   { lang: 'zh', title: '中文标题', url: '/zh/blog/post', available: true }
 * ]
 * ```
 */
export interface LanguageVersion {
  /** Language code */
  lang: string
  /** Content title in this language */
  title: string
  /** URL for this language version */
  url: string
  /** Whether content is available in this language */
  available: boolean
  /** Whether this is the current language */
  current?: boolean
}

/**
 * Content Page Information Interface
 *
 * Represents information about a content page extracted from URL analysis.
 * This is used for routing and language switching functionality.
 *
 * @example
 * ```typescript
 * const pageInfo: ContentPageInfo = {
 *   contentType: 'blog',
 *   slug: 'my-post',
 *   isContentPage: true,
 *   pathSegments: ['blog', 'my-post']
 * }
 * ```
 */
export interface ContentPageInfo {
  /** The type of content detected */
  contentType: ContentType
  /** The content slug */
  slug: string | null
  /** Whether this is a valid content page */
  isContentPage: boolean
  /** Additional path segments */
  pathSegments?: string[]
}

/**
 * SEO Metadata Interface
 *
 * Defines the structure for SEO-related metadata that can be
 * generated from content items for search engine optimization.
 * Includes support for Open Graph, Twitter Cards, and structured data.
 *
 * @example
 * ```typescript
 * const seoData: SEOMetadata = {
 *   title: 'My Blog Post - Site Name',
 *   description: 'A comprehensive guide to...',
 *   keywords: ['tutorial', 'guide', 'programming'],
 *   canonicalUrl: 'https://example.com/blog/my-post',
 *   openGraph: {
 *     title: 'My Blog Post',
 *     description: 'A comprehensive guide...',
 *     image: 'https://example.com/images/post.jpg',
 *     type: 'article'
 *   },
 *   twitterCard: {
 *     card: 'summary_large_image',
 *     title: 'My Blog Post',
 *     description: 'A comprehensive guide...',
 *     image: 'https://example.com/images/post.jpg'
 *   }
 * }
 * ```
 */
export interface SEOMetadata {
  /** Page title for SEO */
  title: string
  /** Meta description */
  description: string
  /** Meta keywords */
  keywords: string[]
  /** Canonical URL */
  canonicalUrl: string
  /** Open Graph metadata */
  openGraph: {
    title: string
    description: string
    image?: string
    type: 'article' | 'website'
    url?: string
    siteName?: string
  }
  /** Twitter Card metadata */
  twitterCard: {
    card: 'summary' | 'summary_large_image'
    title: string
    description: string
    image?: string
    site?: string
    creator?: string
  }
  /** Additional meta tags */
  additionalTags?: Record<string, string>
}

/**
 * Content Type Mapping
 *
 * Maps content type identifiers to their corresponding
 * TypeScript interfaces for type-safe content handling.
 *
 * @example
 * ```typescript
 * type BlogType = ContentTypeMap['blog'] // BlogContent
 * type ProductType = ContentTypeMap['product'] // ProductContent
 * ```
 */
export type ContentTypeMap = {
  blog: BlogContent
  product: ProductContent
  'case-study': CaseStudyContent
}

/**
 * Content By Type Helper
 *
 * Utility type for getting content by type with proper typing.
 * This ensures type safety when working with specific content types.
 *
 * @example
 * ```typescript
 * type BlogContentType = ContentByType<'blog'> // BlogContent
 * type ProductContentType = ContentByType<'product'> // ProductContent
 * ```
 */
export type ContentByType<T extends ContentType> = ContentTypeMap[T]
