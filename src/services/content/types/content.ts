/**
 * Content Type Definitions
 * 
 * This module defines all content-related types used throughout the content service layer.
 * These types provide a consistent interface for content operations across different
 * content management systems and ensure type safety throughout the application.
 */

/**
 * Supported content types in the system
 */
export type ContentType = 'blog' | 'product' | 'case-study'

/**
 * MDX Content Interface
 *
 * Represents processed MDX content with both raw source and compiled code.
 * This structure supports both server-side rendering and client-side hydration
 * by providing access to both the original markdown and the compiled JavaScript.
 */
export interface MDXContent {
  /** Raw markdown/MDX source content */
  raw: string
  /** Compiled JavaScript code for rendering */
  code: string
}

/**
 * Base interface for all content items
 * 
 * This interface defines the common properties that all content items must have,
 * regardless of their specific type. It ensures consistency across different
 * content types and provides a foundation for generic content operations.
 */
export interface ContentItem {
  /** Unique identifier for the content item */
  slug: string
  /** Content title */
  title: string
  /** Content description/excerpt */
  description: string
  /** Publication date */
  date: string
  /** Content language/locale */
  locale: string
  /** Content type identifier */
  type: ContentType
  /** Whether the content is published */
  published: boolean
  /** Content tags/categories */
  tags?: string[]
  /** Featured image URL */
  image?: string
  /** Whether this content is featured/highlighted */
  featured?: boolean
  /** Author name or identifier */
  author?: string
  /** Publication date in ISO 8601 format */
  publishedAt?: string
  /** Creation date in ISO 8601 format */
  createdAt?: string
  /** URL to the cover/featured image */
  coverImage?: string
  /** URL to the author's profile image */
  authorImage?: string
  /** URL to an associated video */
  videoUrl?: string
  /** URL to the video thumbnail image */
  videoThumbnail?: string
  /** Duration of the video in human-readable format */
  videoDuration?: string
  /** SEO-specific metadata */
  seo?: {
    title?: string
    description?: string
    keywords?: string[]
  }
}

/**
 * Blog-specific content interface
 * 
 * Extends the base ContentItem with blog-specific properties
 * such as author information and reading time estimation.
 */
export interface BlogContent extends ContentItem {
  type: 'blog'
  /** Estimated reading time in minutes */
  readingTime?: number
  /** Blog post content body */
  body: MDXContent
  /** Blog category */
  category?: string
}

/**
 * Product-specific content interface
 * 
 * Extends the base ContentItem with product-specific properties
 * such as pricing, features, and product specifications.
 */
export interface ProductContent extends ContentItem {
  type: 'product'
  /** Product price */
  price?: string
  /** Product features list */
  features?: string[]
  /** Product specifications */
  specifications?: Record<string, string>
  /** Product content body */
  body: MDXContent
  /** Product category */
  category?: string
  /** Product availability status */
  available?: boolean
}

/**
 * Case Study-specific content interface
 * 
 * Extends the base ContentItem with case study-specific properties
 * such as client information, project details, and results.
 */
export interface CaseStudyContent extends ContentItem {
  type: 'case-study'
  /** Client name */
  client: string
  /** Industry sector */
  industry?: string
  /** Project duration */
  duration?: string
  /** Key results achieved */
  results?: string[]
  /** Case study content body */
  body: MDXContent
  /** Technologies used */
  technologies?: string[]
}

/**
 * Content query options interface
 * 
 * Defines the available options for querying content lists,
 * including filtering, sorting, and pagination parameters.
 */
export interface QueryOptions {
  /** Maximum number of items to return */
  limit?: number
  /** Number of items to skip (for pagination) */
  offset?: number
  /** Sort field */
  sortBy?: 'date' | 'title' | 'slug'
  /** Sort order */
  sortOrder?: 'asc' | 'desc'
  /** Filter by tags */
  tags?: string[]
  /** Filter by category */
  category?: string
  /** Filter by published status */
  published?: boolean
  /** Search query string */
  search?: string
}

/**
 * Content page information interface
 * 
 * Represents information about a content page extracted from URL analysis.
 * This is used for routing and language switching functionality.
 */
export interface ContentPageInfo {
  /** The type of content detected */
  contentType: ContentType
  /** The content slug */
  slug: string | null
  /** Whether this is a valid content page */
  isContentPage: boolean
  /** Additional path segments */
  pathSegments?: string[]
}

/**
 * SEO metadata interface
 * 
 * Defines the structure for SEO-related metadata that can be
 * generated from content items for search engine optimization.
 */
export interface SEOMetadata {
  /** Page title for SEO */
  title: string
  /** Meta description */
  description: string
  /** Meta keywords */
  keywords: string[]
  /** Canonical URL */
  canonicalUrl: string
  /** Open Graph metadata */
  openGraph: {
    title: string
    description: string
    image?: string
    type: 'article' | 'website'
  }
  /** Twitter Card metadata */
  twitterCard: {
    card: 'summary' | 'summary_large_image'
    title: string
    description: string
    image?: string
  }
}
