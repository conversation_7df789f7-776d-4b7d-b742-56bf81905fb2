/**
 * API and Service Interface Type Definitions
 * 
 * This module defines types for API interfaces, service contracts,
 * and data transfer objects used in the content service layer.
 * These types ensure consistent communication between different
 * layers of the content management system.
 */

import type { ContentType, ContentItem, QueryOptions } from './content'
import type { SupportedLocale, LanguageVersion } from './language'

/**
 * Content service interface
 * 
 * Defines the contract for content data access operations.
 * This interface abstracts the underlying CMS implementation
 * and provides a consistent API for content operations.
 */
export interface IContentService {
  /**
   * Initialize the content service
   */
  initialize(): Promise<void>

  /**
   * Get a single content item by type, slug, and locale
   */
  getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: SupportedLocale
  ): Promise<T | null>

  /**
   * Get a list of content items with optional filtering and sorting
   */
  getContentList<T extends ContentItem>(
    type: ContentType,
    locale: SupportedLocale,
    options?: QueryOptions
  ): Promise<T[]>

  /**
   * Get all content for static generation (build time)
   */
  getContentForStaticGeneration<T extends ContentItem>(
    type: ContentType
  ): Promise<T[]>

  /**
   * Get all content slugs for static generation
   */
  getAllContentSlugs(
    type: ContentType
  ): Promise<Array<{ locale: SupportedLocale; slug: string }>>

  /**
   * Check if content exists in a specific locale
   */
  contentExists(
    type: ContentType,
    slug: string,
    locale: SupportedLocale
  ): Promise<boolean>

  /**
   * Get content title without loading full content
   */
  getContentTitle(
    type: ContentType,
    slug: string,
    locale: SupportedLocale
  ): Promise<string | null>

  /**
   * Get available language versions of content
   */
  getAvailableLanguages(
    type: ContentType,
    slug: string
  ): Promise<LanguageVersion[]>
}

/**
 * Content repository interface
 * 
 * Defines the contract for content data persistence operations.
 * This interface can be implemented by different storage backends
 * such as file systems, databases, or headless CMS APIs.
 */
export interface IContentRepository {
  /**
   * Find content by criteria
   */
  findContent<T extends ContentItem>(
    criteria: ContentSearchCriteria
  ): Promise<T[]>

  /**
   * Find single content item
   */
  findOne<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: SupportedLocale
  ): Promise<T | null>

  /**
   * Check content existence
   */
  exists(
    type: ContentType,
    slug: string,
    locale: SupportedLocale
  ): Promise<boolean>

  /**
   * Get content metadata only
   */
  getMetadata(
    type: ContentType,
    slug: string,
    locale: SupportedLocale
  ): Promise<ContentMetadata | null>
}

/**
 * Content search criteria interface
 * 
 * Defines the criteria for searching and filtering content items.
 * Used by repository implementations to perform efficient queries.
 */
export interface ContentSearchCriteria {
  /** Content type filter */
  type?: ContentType
  /** Locale filter */
  locale?: SupportedLocale
  /** Published status filter */
  published?: boolean
  /** Tag filters */
  tags?: string[]
  /** Category filter */
  category?: string
  /** Search query */
  query?: string
  /** Date range filter */
  dateRange?: {
    from?: string
    to?: string
  }
  /** Sorting options */
  sort?: {
    field: string
    order: 'asc' | 'desc'
  }
  /** Pagination options */
  pagination?: {
    limit: number
    offset: number
  }
}

/**
 * Content metadata interface
 * 
 * Represents lightweight content metadata without the full content body.
 * Useful for operations that only need basic information about content items.
 */
export interface ContentMetadata {
  /** Content slug */
  slug: string
  /** Content title */
  title: string
  /** Content description */
  description: string
  /** Content type */
  type: ContentType
  /** Content locale */
  locale: SupportedLocale
  /** Publication date */
  date: string
  /** Published status */
  published: boolean
  /** Content tags */
  tags: string[]
  /** Featured image URL */
  image?: string
  /** Last modified timestamp */
  lastModified?: string
  /** Content size in bytes */
  size?: number
}

/**
 * Service operation result interface
 * 
 * Represents the result of a service operation with success/error information.
 * Provides a consistent way to handle operation results across the service layer.
 */
export interface ServiceResult<T = any> {
  /** Whether the operation was successful */
  success: boolean
  /** Result data (if successful) */
  data?: T
  /** Error information (if failed) */
  error?: {
    /** Error code */
    code: string
    /** Error message */
    message: string
    /** Additional error details */
    details?: any
  }
  /** Operation metadata */
  metadata?: {
    /** Operation timestamp */
    timestamp: string
    /** Operation duration in milliseconds */
    duration?: number
    /** Cache information */
    cached?: boolean
  }
}

/**
 * Content validation result interface
 * 
 * Represents the result of content validation operations.
 * Used to ensure content integrity and compliance with business rules.
 */
export interface ContentValidationResult {
  /** Whether the content is valid */
  valid: boolean
  /** Validation errors */
  errors: Array<{
    /** Field that failed validation */
    field: string
    /** Error message */
    message: string
    /** Error severity */
    severity: 'error' | 'warning' | 'info'
  }>
  /** Validation warnings */
  warnings: Array<{
    /** Field with warning */
    field: string
    /** Warning message */
    message: string
    /** Suggested action */
    suggestion?: string
  }>
}
