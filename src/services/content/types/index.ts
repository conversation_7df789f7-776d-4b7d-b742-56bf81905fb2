/**
 * Unified Content Service Types - Central Export Hub
 *
 * This module provides a unified interface to all type definitions used
 * throughout the unified content service layer. It serves as the single
 * entry point for importing content-related types, ensuring consistency
 * and ease of use across the entire application.
 *
 * Type Categories:
 * - Content Types: Core content item definitions and interfaces
 * - Provider Types: CMS provider interfaces and contracts
 * - Configuration Types: Service and provider configuration
 * - Language Types: Internationalization and language switching types
 * - API Types: Service interfaces and data transfer objects
 *
 * Architecture Benefits:
 * - Single source of truth for all content-related types
 * - Consistent type definitions across CMS providers
 * - Type-safe content operations and configurations
 * - Comprehensive internationalization support
 * - Provider-agnostic interfaces for future extensibility
 *
 * Usage:
 * ```typescript
 * import type {
 *   ContentType,
 *   BlogContent,
 *   ContentProvider,
 *   ContentServiceConfig,
 *   LanguageSwitchResult,
 *   IContentService
 * } from '@/services/content/types'
 * ```
 *
 * <AUTHOR> Service Team
 * @since 2.0.0 (Unified Architecture)
 */

// ==========================================
// Content Types - Core Definitions
// ==========================================

export type {
  // Core content types
  ContentType,
  MDXContent,
  BaseContentItem,
  ContentItem,
  ContentItemUnion,

  // Specific content types
  BlogContent,
  ProductContent,
  CaseStudyContent,

  // Content operations
  QueryOptions,
  ContentMetadata,
  LanguageVersion,
  ContentPageInfo,
  SEOMetadata,

  // Type utilities
  ContentTypeMap,
  ContentByType
} from './content'

// ==========================================
// Provider Types - CMS Abstraction
// ==========================================

export type {
  ContentProvider,
  ProviderFactory,
  ProviderRegistry
} from './provider'

// ==========================================
// Configuration Types - Service Setup
// ==========================================

export type {
  CMSProviderType,
  ContentServiceFeatures,
  I18nConfig,
  PerformanceConfig,
  ProviderConfig,
  ContentServiceConfig
} from './config'

// Export configuration utilities
export {
  developmentConfig,
  productionConfig,
  testingConfig,
  mergeConfigWithEnvironment
} from './config'

// ==========================================
// Language Types - Internationalization
// ==========================================

export type {
  SupportedLocale,
  LanguageVersion as LanguageVersionDetailed,
  LanguageSwitchParams,
  LanguageSwitchResult,
  LanguageDetectionResult,
  I18nConfig as I18nConfigDetailed,
  ContentLocalizationMetadata
} from './language'

// ==========================================
// API Types - Service Interfaces
// ==========================================

export type {
  IContentService,
  ServiceResult,
  ContentSearchCriteria,
  ContentMetadata as ContentMetadataAPI
} from './api'

// ==========================================
// Utility Types and Type Guards
// ==========================================

/**
 * Content Service Initialization Result
 *
 * Represents the result of content service initialization,
 * including status information and any initialization errors.
 *
 * @example
 * ```typescript
 * const initResult: ContentServiceInitResult = {
 *   success: true,
 *   timestamp: '2024-01-01T00:00:00Z',
 *   config: myConfig,
 *   provider: {
 *     name: 'contentlayer2',
 *     version: '0.4.6',
 *     status: 'ready'
 *   }
 * }
 * ```
 */
export interface ContentServiceInitResult {
  /** Whether initialization was successful */
  success: boolean
  /** Initialization timestamp */
  timestamp: string
  /** Configuration used for initialization */
  config: ContentServiceConfig
  /** Provider information */
  provider: {
    /** Provider name */
    name: string
    /** Provider version */
    version?: string
    /** Provider status */
    status: 'ready' | 'error' | 'initializing'
  }
  /** Initialization errors (if any) */
  errors?: Array<{
    /** Error code */
    code: string
    /** Error message */
    message: string
    /** Error context */
    context?: string
  }>
  /** Performance metrics */
  metrics?: {
    /** Initialization duration in milliseconds */
    initDuration: number
    /** Number of content items loaded */
    contentCount: number
    /** Memory usage in bytes */
    memoryUsage?: number
  }
}

/**
 * Content operation context interface
 * 
 * Provides context information for content operations,
 * useful for logging, debugging, and performance monitoring.
 */
export interface ContentOperationContext {
  /** Operation identifier */
  operationId: string
  /** Operation type */
  operation: 'read' | 'list' | 'search' | 'validate'
  /** Request timestamp */
  timestamp: string
  /** User context (if available) */
  user?: {
    /** User identifier */
    id: string
    /** User locale preference */
    locale: 'en' | 'zh'
    /** User permissions */
    permissions?: string[]
  }
  /** Request metadata */
  request?: {
    /** Request source */
    source: 'ssr' | 'csr' | 'static' | 'api'
    /** Request URL */
    url?: string
    /** User agent */
    userAgent?: string
  }
}

/**
 * Type Guards and Validation Utilities
 *
 * Provides runtime type checking functions for content-related types.
 * These functions help ensure type safety when working with dynamic data.
 */

/**
 * Type guard to check if an object is a valid ContentItem
 *
 * @param obj - Object to check
 * @returns True if object is a valid ContentItem
 *
 * @example
 * ```typescript
 * if (isContentItem(data)) {
 *   // data is now typed as ContentItem
 *   console.log(data.title)
 * }
 * ```
 */
export function isContentItem(obj: any): obj is import('./content').ContentItem {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.slug === 'string' &&
    typeof obj.title === 'string' &&
    typeof obj.type === 'string' &&
    ['blog', 'product', 'case-study'].includes(obj.type) &&
    (typeof obj.lang === 'string' || typeof obj.locale === 'string')
  )
}

/**
 * Type guard to check if a string is a valid ContentType
 *
 * @param str - String to check
 * @returns True if string is a valid ContentType
 */
export function isContentType(str: string): str is import('./content').ContentType {
  return ['blog', 'product', 'case-study'].includes(str)
}

/**
 * Type guard to check if a string is a valid SupportedLocale
 *
 * @param str - String to check
 * @returns True if string is a valid SupportedLocale
 */
export function isSupportedLocale(str: string): str is import('./language').SupportedLocale {
  return ['en', 'zh'].includes(str)
}

/**
 * Type guard to check if an object is a valid MDXContent
 *
 * @param obj - Object to check
 * @returns True if object is a valid MDXContent
 */
export function isMDXContent(obj: any): obj is import('./content').MDXContent {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.raw === 'string' &&
    typeof obj.code === 'string'
  )
}

/**
 * Utility Types for Content Operations
 *
 * Provides helper types for common content operations and data structures.
 */

/**
 * Utility type for partial content updates
 *
 * Allows partial updates to content items while preserving required fields.
 *
 * @example
 * ```typescript
 * const update: PartialContentItem<BlogContent> = {
 *   slug: 'my-post',
 *   type: 'blog',
 *   title: 'Updated Title'
 * }
 * ```
 */
export type PartialContentItem<T extends import('./content').ContentItem> = Partial<Omit<T, 'slug' | 'type'>> & {
  slug: string
  type: import('./content').ContentType
}

/**
 * Utility type for content list responses with pagination
 *
 * Provides a standardized structure for paginated content responses.
 *
 * @example
 * ```typescript
 * const response: ContentListResponse<BlogContent> = {
 *   items: blogPosts,
 *   total: 100,
 *   page: 1,
 *   pageSize: 10,
 *   hasMore: true
 * }
 * ```
 */
export type ContentListResponse<T extends import('./content').ContentItem> = {
  items: T[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
}

/**
 * Utility type for content operation results
 *
 * Provides a standardized result type for content operations.
 */
export type ContentOperationResult<T = any> = {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  metadata?: {
    timestamp: string
    duration?: number
    cached?: boolean
  }
}
