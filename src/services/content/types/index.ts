/**
 * Content Service Types - Unified Export
 * 
 * This module provides a unified interface to all type definitions used
 * throughout the content service layer. It serves as the single entry point
 * for importing content-related types, ensuring consistency and ease of use.
 * 
 * Type Categories:
 * - Content Types: Core content item definitions and interfaces
 * - Language Types: Internationalization and language switching types
 * - API Types: Service interfaces and data transfer objects
 * 
 * Usage:
 * ```typescript
 * import type {
 *   ContentType,
 *   BlogContent,
 *   LanguageSwitchResult,
 *   IContentService
 * } from '@/services/content/types'
 * ```
 */

// Content-related types
export type {
  ContentType,
  ContentItem,
  BlogContent,
  ProductContent,
  CaseStudyContent,
  QueryOptions,
  ContentPageInfo,
  SEOMetadata
} from './content'

// Language and internationalization types
export type {
  SupportedLocale,
  LanguageVersion,
  LanguageSwitchParams,
  LanguageSwitchResult,
  LanguageDetectionResult,
  I18nConfig,
  ContentLocalizationMetadata
} from './language'

// API and service interface types
export type {
  IContentService,
  IContentRepository,
  ContentSearchCriteria,
  ContentMetadata,
  ServiceResult,
  ContentValidationResult
} from './api'

/**
 * Content service configuration interface
 * 
 * Defines the configuration options for initializing the content service layer.
 * This interface combines settings from different aspects of the content system.
 */
export interface ContentServiceConfig {
  /** CMS provider configuration */
  provider: {
    /** Provider type */
    type: 'contentlayer2' | 'strapi' | 'nextjs-mdx'
    /** Provider-specific options */
    options?: Record<string, any>
  }
  
  /** Content types to support */
  contentTypes: Array<'blog' | 'product' | 'case-study'>

  /** Internationalization configuration */
  i18n: {
    /** Default locale */
    defaultLocale: 'en' | 'zh'
    /** Supported locales */
    supportedLocales: Array<'en' | 'zh'>
    /** Language switching settings */
    languageSwitching: boolean
  }
  
  /** Feature flags */
  features: {
    /** Enable SEO metadata generation */
    seo: boolean
    /** Enable related content suggestions */
    relatedContent: boolean
    /** Enable content validation */
    validation: boolean
    /** Enable static generation support */
    staticGeneration: boolean
  }
  
  /** Performance settings */
  performance?: {
    /** Enable content preloading */
    preloading: boolean
    /** Content list page size */
    pageSize: number
    /** Enable content compression */
    compression: boolean
  }
}

/**
 * Content service initialization result
 * 
 * Represents the result of content service initialization,
 * including status information and any initialization errors.
 */
export interface ContentServiceInitResult {
  /** Whether initialization was successful */
  success: boolean
  /** Initialization timestamp */
  timestamp: string
  /** Configuration used for initialization */
  config: ContentServiceConfig
  /** Provider information */
  provider: {
    /** Provider name */
    name: string
    /** Provider version */
    version?: string
    /** Provider status */
    status: 'ready' | 'error' | 'initializing'
  }
  /** Initialization errors (if any) */
  errors?: Array<{
    /** Error code */
    code: string
    /** Error message */
    message: string
    /** Error context */
    context?: string
  }>
  /** Performance metrics */
  metrics?: {
    /** Initialization duration in milliseconds */
    initDuration: number
    /** Number of content items loaded */
    contentCount: number
    /** Memory usage in bytes */
    memoryUsage?: number
  }
}

/**
 * Content operation context interface
 * 
 * Provides context information for content operations,
 * useful for logging, debugging, and performance monitoring.
 */
export interface ContentOperationContext {
  /** Operation identifier */
  operationId: string
  /** Operation type */
  operation: 'read' | 'list' | 'search' | 'validate'
  /** Request timestamp */
  timestamp: string
  /** User context (if available) */
  user?: {
    /** User identifier */
    id: string
    /** User locale preference */
    locale: 'en' | 'zh'
    /** User permissions */
    permissions?: string[]
  }
  /** Request metadata */
  request?: {
    /** Request source */
    source: 'ssr' | 'csr' | 'static' | 'api'
    /** Request URL */
    url?: string
    /** User agent */
    userAgent?: string
  }
}

/**
 * Type guards and utility types
 */

/**
 * Type guard to check if an object is a valid ContentItem
 */
export function isContentItem(obj: any): obj is import('./content').ContentItem {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.slug === 'string' &&
    typeof obj.title === 'string' &&
    typeof obj.type === 'string' &&
    ['blog', 'product', 'case-study'].includes(obj.type)
  )
}

/**
 * Type guard to check if a string is a valid ContentType
 */
export function isContentType(str: string): str is import('./content').ContentType {
  return ['blog', 'product', 'case-study'].includes(str)
}

/**
 * Type guard to check if a string is a valid SupportedLocale
 */
export function isSupportedLocale(str: string): str is import('./language').SupportedLocale {
  return ['en', 'zh'].includes(str)
}

/**
 * Utility type for partial content updates
 */
export type PartialContentItem<T extends import('./content').ContentItem> = Partial<Omit<T, 'slug' | 'type'>> & {
  slug: string
  type: import('./content').ContentType
}

/**
 * Utility type for content list responses
 */
export type ContentListResponse<T extends import('./content').ContentItem> = {
  items: T[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
}
