/**
 * Content Service Configuration Types
 * 
 * This module defines configuration interfaces and types for the unified
 * content service system. The configuration system provides flexible setup 
 * options for different CMS providers, feature flags, performance settings, 
 * and deployment environments.
 * 
 * The configuration types support multiple CMS backends and provide
 * fine-grained control over system behavior, caching strategies,
 * and feature enablement.
 * 
 * Key Features:
 * - Multi-provider CMS configuration
 * - Feature flag system for optional capabilities
 * - Performance and caching configuration
 * - Multilingual content support settings
 * - Environment-specific configuration options
 * - Type-safe configuration validation
 * 
 * <AUTHOR> Service Team
 * @since 2.0.0 (Unified Architecture)
 */

import type { ContentType } from './content'

/**
 * Supported CMS Provider Types
 * 
 * Defines the available CMS providers that can be configured
 * in the system. Each provider has its own specific configuration
 * requirements and capabilities.
 * 
 * @example
 * ```typescript
 * const provider: CMSProviderType = 'contentlayer2'
 * ```
 */
export type CMSProviderType = 
  | 'contentlayer2'  // File-based CMS with TypeScript support
  | 'strapi'         // Headless CMS with REST/GraphQL API
  | 'sanity'         // Real-time headless CMS
  | 'contentful'     // Cloud-based headless CMS
  | 'nextjs-mdx'     // Next.js built-in MDX support

/**
 * Content Service Feature Configuration
 * 
 * Defines which optional features are enabled in the content service system.
 * Feature flags allow for gradual rollout of new capabilities
 * and environment-specific feature sets.
 * 
 * @example
 * ```typescript
 * const features: ContentServiceFeatures = {
 *   seo: true,
 *   relatedContent: false,
 *   languageSwitching: true,
 *   staticGeneration: true,
 *   validation: true
 * }
 * ```
 */
export interface ContentServiceFeatures {
  /** Enable SEO data generation and optimization */
  seo: boolean
  /** Enable related content suggestions */
  relatedContent: boolean
  /** Enable language switching functionality */
  languageSwitching: boolean
  /** Enable static generation support */
  staticGeneration: boolean
  /** Enable content validation */
  validation: boolean
}

/**
 * Internationalization Configuration
 *
 * Defines settings for multilingual content support,
 * including default locale, supported languages, and
 * language switching behavior.
 *
 * @example
 * ```typescript
 * const i18nConfig: I18nConfig = {
 *   defaultLocale: 'en',
 *   supportedLocales: ['en', 'zh'],
 *   languageSwitching: true,
 *   fallbackStrategy: 'redirect'
 * }
 * ```
 */
export interface I18nConfig {
  /** Default locale for content */
  defaultLocale: string
  /** List of supported locales */
  supportedLocales: string[]
  /** Enable language switching functionality */
  languageSwitching: boolean
  /** Fallback strategy when content is not available in requested locale */
  fallbackStrategy: 'redirect' | 'show-default' | 'show-404'
}

/**
 * Performance Configuration
 *
 * Defines performance-related settings including caching,
 * preloading, and optimization options.
 *
 * @example
 * ```typescript
 * const perfConfig: PerformanceConfig = {
 *   preloading: false,
 *   pageSize: 10,
 *   compression: false
 * }
 * ```
 */
export interface PerformanceConfig {
  /** Enable content preloading */
  preloading: boolean
  /** Default page size for content lists */
  pageSize: number
  /** Enable content compression */
  compression: boolean
}

/**
 * Provider Configuration
 *
 * Defines configuration for a specific CMS provider,
 * including provider type and provider-specific options.
 *
 * @example
 * ```typescript
 * const providerConfig: ProviderConfig = {
 *   type: 'contentlayer2',
 *   options: {
 *     contentDir: './content',
 *     outputDir: './.contentlayer'
 *   }
 * }
 * ```
 */
export interface ProviderConfig {
  /** CMS provider type */
  type: CMSProviderType
  /** Provider-specific configuration options */
  options?: Record<string, any>
}

/**
 * Content Service Configuration
 *
 * Main configuration interface for the unified content service.
 * This interface combines all configuration aspects into a
 * single, comprehensive configuration object.
 *
 * @example
 * ```typescript
 * const config: ContentServiceConfig = {
 *   provider: {
 *     type: 'contentlayer2',
 *     options: {}
 *   },
 *   contentTypes: ['blog', 'product', 'case-study'],
 *   i18n: {
 *     defaultLocale: 'en',
 *     supportedLocales: ['en', 'zh'],
 *     languageSwitching: true,
 *     fallbackStrategy: 'redirect'
 *   },
 *   features: {
 *     seo: true,
 *     relatedContent: true,
 *     languageSwitching: true,
 *     staticGeneration: true,
 *     validation: true
 *   },
 *   performance: {
 *     preloading: false,
 *     pageSize: 10,
 *     compression: false
 *   }
 * }
 * ```
 */
export interface ContentServiceConfig {
  /** CMS provider configuration */
  provider: ProviderConfig
  /** Content types to support */
  contentTypes: ContentType[]
  /** Internationalization configuration */
  i18n: I18nConfig
  /** Feature flags */
  features: ContentServiceFeatures
  /** Performance settings */
  performance: PerformanceConfig
}

/**
 * Environment-specific Configuration Presets
 */

/** Development environment configuration */
export const developmentConfig: Partial<ContentServiceConfig> = {
  features: {
    seo: false,
    relatedContent: true,
    languageSwitching: true,
    staticGeneration: false,
    validation: true
  },
  performance: {
    preloading: false,
    pageSize: 5,
    compression: false
  }
}

/** Production environment configuration */
export const productionConfig: Partial<ContentServiceConfig> = {
  features: {
    seo: true,
    relatedContent: true,
    languageSwitching: true,
    staticGeneration: true,
    validation: false
  },
  performance: {
    preloading: true,
    pageSize: 10,
    compression: true
  }
}

/** Testing environment configuration */
export const testingConfig: Partial<ContentServiceConfig> = {
  features: {
    seo: false,
    relatedContent: false,
    languageSwitching: false,
    staticGeneration: false,
    validation: true
  },
  performance: {
    preloading: false,
    pageSize: 3,
    compression: false
  }
}

/**
 * Merge configuration with environment-specific settings
 * 
 * @param baseConfig - Base configuration
 * @param envConfig - Environment-specific configuration
 * @returns Merged configuration
 */
export function mergeConfigWithEnvironment(
  baseConfig: ContentServiceConfig,
  envConfig: Partial<ContentServiceConfig>
): ContentServiceConfig {
  return {
    ...baseConfig,
    ...envConfig,
    features: {
      ...baseConfig.features,
      ...(envConfig.features || {})
    },
    performance: {
      ...baseConfig.performance,
      ...(envConfig.performance || {})
    },
    i18n: {
      ...baseConfig.i18n,
      ...(envConfig.i18n || {})
    }
  }
}
