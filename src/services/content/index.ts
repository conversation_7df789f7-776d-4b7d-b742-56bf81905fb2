/**
 * Content Services - Unified Export Interface
 * 
 * This module provides the main entry point for all content-related services
 * in the application. It implements a layered architecture with clear separation
 * of concerns between data access, business logic, and presentation utilities.
 * 
 * Architecture Layers:
 * - Core Layer: Direct data access and fundamental operations
 * - Features Layer: Business logic and advanced functionality
 * - Utils Layer: Presentation helpers and utility functions
 * - Types Layer: Type definitions and interfaces
 * 
 * Usage Examples:
 * ```typescript
 * // Core data operations
 * import { contentService } from '@/services/content'
 * const blog = await contentService.getContent('blog', 'my-post', 'en')
 * 
 * // Business features
 * import { languageSwitching, seoMetadata } from '@/services/content'
 * const switchResult = await languageSwitching.handleContentLanguageSwitch(params)
 * const metadata = seoMetadata.generateSEOMetadata(content)
 * 
 * // Utility functions
 * import { generateContentUrl, validateContentSlug } from '@/services/content'
 * const url = generateContentUrl('blog', 'my-post', 'en')
 * const isValid = validateContentSlug('my-post')
 * ```
 * 
 * Migration Strategy:
 * This new architecture replaces the previous unified-content.ts approach
 * with a more modular and maintainable structure. The migration preserves
 * all existing functionality while providing better organization and
 * extensibility for future enhancements.
 */

// ==========================================
// Core Services - Data Layer
// ==========================================

// Main content service (primary interface)
export {
  contentService,
  createContentService
} from './core'

// CMS Adapter (direct data access)
export {
  cmsAdapter,
  getContentForStaticGeneration,
  getAllContentSlugs,
  getContent,
  getContentList,
  contentExists,
  getContentTitle,
  getAvailableLanguages
} from './core'

// Content Queries (high-level data operations)
export {
  contentQueries,
  contentExistsInLocale,
  checkContentAvailability,
  getContentTitles,
  getPublishedContentList,
  getRecentContent,
  getContentByCategory,
  searchContent,
  getAllContentForStaticGeneration,
  getContentSlugsForStaticParams
} from './core'

// Static Generation (build-time operations)
export {
  staticGeneration,
  generateStaticParams,
  generateAllStaticParams,
  getAllContent,
  getContentByLocale,
  getPublishedContent,
  getContentStatistics,
  validateContentForBuild
} from './core'

// ==========================================
// Feature Services - Business Logic Layer
// ==========================================

// Language Switching
export {
  languageSwitching,
  getAvailableLanguageVersions,
  getLanguageAvailabilitySummary,
  handleContentLanguageSwitch,
  generateHreflangAttributes,
  getLanguageSwitchAnalytics
} from './features'

// SEO Metadata
export {
  seoMetadata,
  generateSEOMetadata,
  generateListPageSEOMetadata
} from './features'

// Content Relations
export {
  contentRelations,
  findRelatedContent,
  getContentRecommendations
} from './features'

// Features Manager (coordinated operations)
export {
  contentFeatures
} from './features'

// ==========================================
// Utility Functions - Presentation Layer
// ==========================================

// Content Detection
export {
  detectContentPage,
  getContentBasePath,
  isContentListPage,
  isContentDetailPage,
  extractContentType,
  configureContentDetection
} from './utils'

// URL Generation
export {
  generateContentUrl,
  generateCanonicalUrl,
  generateAlternateUrls,
  generateContentListUrl,
  parseContentUrl,
  validateContentUrl,
  configureUrlGeneration
} from './utils'

// Validation
export {
  validateContentSlug,
  validateContentType,
  validateLocale,
  validateContentItem,
  validateSEOMetadata,
  sanitizeSlug,
  checkContentCompleteness
} from './utils'

// Utility Collections
export {
  ContentUtils,
  TypeGuards,
  configureContentUtils
} from './utils'

// ==========================================
// Type Definitions
// ==========================================

// Core Types
export type {
  ContentType,
  ContentItem,
  BlogContent,
  ProductContent,
  CaseStudyContent,
  QueryOptions,
  ContentPageInfo,
  SEOMetadata
} from './types'

// Language Types
export type {
  SupportedLocale,
  LanguageVersion,
  LanguageSwitchParams,
  LanguageSwitchResult,
  LanguageDetectionResult,
  I18nConfig,
  ContentLocalizationMetadata
} from './types'

// API Types
export type {
  IContentService,
  IContentRepository,
  ContentSearchCriteria,
  ContentMetadata,
  ServiceResult,
  ContentValidationResult,
  ContentServiceConfig,
  ContentServiceInitResult,
  ContentOperationContext
} from './types'

// Type Guards
export {
  isContentItem,
  isContentType,
  isSupportedLocale
} from './types'

// Import service instances for UnifiedContentService
import { contentService } from './core'
import { contentFeatures } from './features'
import {
  detectContentPage,
  isContentListPage,
  isContentDetailPage,
  extractContentType,
  generateContentUrl,
  generateCanonicalUrl,
  generateContentListUrl,
  parseContentUrl,
  validateContentSlug,
  validateContentType,
  validateLocale,
  validateContentItem,
  sanitizeSlug,
  ContentUtils,
  TypeGuards
} from './utils'

// ==========================================
// Main Service Interface
// ==========================================

/**
 * Unified Content Service Interface
 *
 * Provides a single, comprehensive interface to all content services.
 * This is the recommended way to access content functionality in most cases.
 *
 * The service combines all layers (core, features, utils) into a cohesive
 * interface while maintaining the ability to access individual services
 * when more granular control is needed.
 */
export class UnifiedContentService {
  /**
   * Core data services
   */
  readonly core = contentService

  /**
   * Business feature services
   */
  readonly features = contentFeatures

  /**
   * Utility functions
   */
  readonly utils = {
    // Content detection utilities
    detectContentPage,
    isContentListPage,
    isContentDetailPage,
    extractContentType,
    
    // URL generation utilities
    generateContentUrl,
    generateCanonicalUrl,
    generateContentListUrl,
    parseContentUrl,
    
    // Validation utilities
    validateContentSlug,
    validateContentType,
    validateLocale,
    validateContentItem,
    sanitizeSlug,
    
    // Utility collections
    ContentUtils,
    TypeGuards
  }

  /**
   * Initialize all services
   * 
   * @param config - Optional service configuration
   */
  async initialize(config?: any): Promise<void> {
    await this.core.initialize(config)
  }

  /**
   * Check if services are initialized
   */
  isInitialized(): boolean {
    return this.core.isInitialized()
  }

  /**
   * Reset all services (useful for testing)
   */
  reset(): void {
    this.core.reset()
  }
}

// Create and export singleton instance
export const unifiedContentService = new UnifiedContentService()

// Export as default for convenience
export default unifiedContentService
