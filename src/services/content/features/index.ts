/**
 * Content Features - Unified Export
 * 
 * This module provides a unified interface to all content feature services.
 * Features implement business logic that builds upon the core data layer
 * to provide rich functionality for content management and user experience.
 * 
 * Feature Services:
 * - Language Switching: Intelligent multilingual navigation
 * - SEO Metadata: Comprehensive SEO optimization
 * - Content Relations: Content discovery and recommendations
 * 
 * Usage:
 * ```typescript
 * import {
 *   languageSwitching,
 *   seoMetadata,
 *   contentRelations
 * } from '@/services/content/features'
 * ```
 * 
 * Architecture:
 * The features layer implements business logic that coordinates between
 * the core data layer and presentation utilities. These services provide
 * high-level functionality that directly supports user-facing features.
 */

// Language Switching Service
export {
  languageSwitching,
  getAvailableLanguageVersions,
  getLanguageAvailabilitySummary,
  handleContentLanguageSwitch,
  generateHreflangAttributes,
  getLanguageSwitchAnalytics
} from './language-switching'

// SEO Metadata Service
export {
  seoMetadata,
  generateSEOMetadata,
  generateListPageSEOMetadata
} from './seo-metadata'

// Content Relations Service
export {
  contentRelations,
  findRelatedContent,
  getContentRecommendations
} from './content-relations'

// Re-export types for convenience
export type {
  LanguageVersion,
  LanguageSwitchParams,
  LanguageSwitchResult,
  SEOMetadata,
  ContentType,
  SupportedLocale
} from '../types'

// Import service instances for ContentFeaturesManager
import { languageSwitching } from './language-switching'
import { seoMetadata } from './seo-metadata'
import { contentRelations } from './content-relations'

/**
 * Content Features Manager
 *
 * Provides a unified interface to all content feature services,
 * allowing for coordinated operations across multiple features.
 */
export class ContentFeaturesManager {
  /**
   * Language Switching service instance
   */
  readonly language = languageSwitching

  /**
   * SEO Metadata service instance
   */
  readonly seo = seoMetadata

  /**
   * Content Relations service instance
   */
  readonly relations = contentRelations

  // ==========================================
  // Coordinated Feature Operations
  // ==========================================

  /**
   * Generate complete page metadata with language support
   * 
   * Combines SEO metadata generation with language version information
   * to create comprehensive page metadata for multilingual sites.
   * 
   * @param content - Content item
   * @param options - Generation options
   * @returns Complete page metadata
   */
  async generateCompletePageMetadata(
    content: any,
    options: {
      baseUrl?: string
      siteName?: string
      currentLocale?: any
    } = {}
  ) {
    const { baseUrl = '', siteName = 'ShipAny', currentLocale = 'en' } = options

    // Get available language versions
    const languageVersions = await this.language.getAvailableLanguageVersions(
      content.type,
      content.slug,
      currentLocale
    )

    // Generate SEO metadata
    const seoMetadata = this.seo.generateSEOMetadata(content, {
      baseUrl,
      siteName,
      availableLocales: languageVersions
        .filter(v => v.available)
        .map(v => v.lang)
    })

    // Generate hreflang attributes
    const hreflangAttributes = await this.language.generateHreflangAttributes(
      content.type,
      content.slug,
      baseUrl
    )

    return {
      seo: seoMetadata,
      languages: languageVersions,
      hreflang: hreflangAttributes
    }
  }

  /**
   * Generate content page bundle
   * 
   * Creates a complete bundle of content data and related information
   * needed for rendering a content page, including related content,
   * language versions, and SEO metadata.
   * 
   * @param content - Main content item
   * @param options - Bundle generation options
   * @returns Complete content page bundle
   */
  async generateContentPageBundle(
    content: any,
    options: {
      includeRelated?: boolean
      includeSEO?: boolean
      includeLanguages?: boolean
      relatedLimit?: number
      baseUrl?: string
      siteName?: string
    } = {}
  ) {
    const {
      includeRelated = true,
      includeSEO = true,
      includeLanguages = true,
      relatedLimit = 5,
      baseUrl = '',
      siteName = 'ShipAny'
    } = options

    const bundle: any = {
      content
    }

    // Generate related content if requested
    if (includeRelated) {
      bundle.related = await this.relations.findRelatedContent(content, {
        limit: relatedLimit
      })
    }

    // Generate SEO metadata if requested
    if (includeSEO) {
      bundle.seo = this.seo.generateSEOMetadata(content, {
        baseUrl,
        siteName
      })
    }

    // Generate language information if requested
    if (includeLanguages) {
      bundle.languages = await this.language.getAvailableLanguageVersions(
        content.type,
        content.slug,
        content.locale
      )

      // Add hreflang attributes for SEO
      if (includeSEO) {
        bundle.hreflang = await this.language.generateHreflangAttributes(
          content.type,
          content.slug,
          baseUrl
        )
      }
    }

    return bundle
  }

  /**
   * Generate content list page bundle
   * 
   * Creates a complete bundle for content list pages including
   * SEO metadata, recommendations, and language information.
   * 
   * @param contentType - Type of content list
   * @param locale - Page locale
   * @param options - Bundle generation options
   * @returns Complete list page bundle
   */
  async generateListPageBundle(
    contentType: any,
    locale: any,
    options: {
      includeSEO?: boolean
      includeRecommendations?: boolean
      recommendationLimit?: number
      baseUrl?: string
      siteName?: string
      category?: string
      page?: number
    } = {}
  ) {
    const {
      includeSEO = true,
      includeRecommendations = true,
      recommendationLimit = 6,
      baseUrl = '',
      siteName = 'ShipAny',
      category,
      page = 1
    } = options

    const bundle: any = {
      contentType,
      locale,
      category,
      page
    }

    // Generate SEO metadata if requested
    if (includeSEO) {
      bundle.seo = this.seo.generateListPageSEOMetadata(contentType, locale, {
        baseUrl,
        siteName,
        category,
        page
      })
    }

    // Generate content recommendations if requested
    if (includeRecommendations) {
      bundle.recommendations = await this.relations.getContentRecommendations({
        contentType,
        locale,
        limit: recommendationLimit,
        strategy: 'mixed'
      })
    }

    return bundle
  }

  // ==========================================
  // Analytics and Insights
  // ==========================================

  /**
   * Get content analytics data
   * 
   * Provides analytics data about content performance,
   * language availability, and user engagement patterns.
   * 
   * @param content - Content item to analyze
   * @returns Analytics data object
   */
  async getContentAnalytics(content: any) {
    // Get language availability summary
    const languageSummary = await this.language.getLanguageAvailabilitySummary(
      content.type,
      content.slug
    )

    return {
      content: {
        type: content.type,
        slug: content.slug,
        title: content.title,
        published: content.published,
        date: content.date
      },
      languages: languageSummary,
      seo: {
        hasCustomTitle: !!content.seo?.title,
        hasCustomDescription: !!content.seo?.description,
        hasKeywords: !!(content.seo?.keywords && content.seo.keywords.length > 0),
        hasFeaturedImage: !!content.image,
        tagCount: content.tags?.length || 0
      }
    }
  }

  /**
   * Validate content completeness
   * 
   * Checks if content has all recommended elements for
   * optimal SEO and user experience.
   * 
   * @param content - Content item to validate
   * @returns Validation results
   */
  async validateContentCompleteness(content: any) {
    const issues: string[] = []
    const recommendations: string[] = []

    // Check basic content elements
    if (!content.title) issues.push('Missing title')
    if (!content.description) issues.push('Missing description')
    if (!content.image) recommendations.push('Add featured image')
    if (!content.tags || content.tags.length === 0) {
      recommendations.push('Add tags for better discoverability')
    }

    // Check SEO elements
    if (!content.seo?.title) recommendations.push('Add custom SEO title')
    if (!content.seo?.description) recommendations.push('Add custom SEO description')

    // Check language availability
    const languageSummary = await this.language.getLanguageAvailabilitySummary(
      content.type,
      content.slug
    )

    if (languageSummary.missingLanguages.length > 0) {
      recommendations.push(
        `Consider translating to: ${languageSummary.missingLanguages.join(', ')}`
      )
    }

    return {
      valid: issues.length === 0,
      issues,
      recommendations,
      completeness: {
        score: Math.max(0, 100 - (issues.length * 20) - (recommendations.length * 5)),
        total: 100
      }
    }
  }
}

// Create and export singleton instance
export const contentFeatures = new ContentFeaturesManager()

// Export default as the main features manager
export default contentFeatures
