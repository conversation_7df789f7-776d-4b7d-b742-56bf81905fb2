/**
 * Language Switching Service - Business Logic Layer
 * 
 * This module provides intelligent language switching functionality for content pages.
 * It implements business logic for smart navigation between different language versions
 * of content with sophisticated fallback strategies when content doesn't exist.
 * 
 * Key Features:
 * - Intelligent language switching with multiple fallback strategies
 * - Content availability checking across all supported locales
 * - User-friendly fallback navigation to prevent 404 errors
 * - Comprehensive language version information for UI components
 * - SEO-friendly URL generation for alternate language versions
 * 
 * Switching Strategies:
 * 1. 'direct': Content exists in target language - navigate directly
 * 2. 'fallback': Content doesn't exist - navigate to content list page
 * 3. 'home': Neither content nor list available - navigate to homepage
 * 
 * Architecture:
 * This service implements business logic that coordinates between the core
 * data layer and presentation utilities to provide a complete language
 * switching experience.
 */

import { contentQueries } from '../core'
import { generateContentUrl, generateContentListUrl } from '../utils'
import type { 
  ContentType, 
  SupportedLocale,
  LanguageVersion,
  LanguageSwitchParams,
  LanguageSwitchResult,
  ContentPageInfo
} from '../types'

/**
 * Language Switching Service Class
 * 
 * Provides comprehensive language switching functionality with intelligent
 * fallback strategies and user experience optimizations.
 */
export class LanguageSwitchingService {

  // ==========================================
  // Language Version Information
  // ==========================================

  /**
   * Get available language versions for content
   * 
   * Creates a comprehensive list of language versions for a specific content item,
   * including availability status, URLs, and titles for each supported locale.
   * Used by UI components to display language switching options.
   * 
   * @param contentType - Type of content
   * @param slug - Content identifier
   * @param currentLocale - Current locale for context
   * @returns Promise resolving to array of language versions
   */
  async getAvailableLanguageVersions(
    contentType: ContentType,
    slug: string,
    currentLocale: SupportedLocale = 'en'
  ): Promise<LanguageVersion[]> {
    const supportedLocales: SupportedLocale[] = ['en', 'zh']
    
    // For non-content pages or missing slugs, mark all languages as unavailable
    if (!slug) {
      return supportedLocales.map(lang => ({
        lang,
        title: '',
        url: generateContentListUrl(contentType, lang),
        available: false,
        current: lang === currentLocale
      }))
    }

    // Check availability and get titles for all locales in parallel
    const versionPromises = supportedLocales.map(async (lang) => {
      const [available, title] = await Promise.all([
        contentQueries.contentExistsInLocale(contentType, slug, lang),
        contentQueries.getContentTitle(contentType, slug, lang)
      ])

      return {
        lang,
        title: title || '',
        url: available 
          ? generateContentUrl(contentType, slug, lang)
          : generateContentListUrl(contentType, lang),
        available,
        current: lang === currentLocale
      }
    })

    return Promise.all(versionPromises)
  }

  /**
   * Get language availability summary
   * 
   * Provides a quick summary of language availability for content,
   * useful for analytics and content management insights.
   * 
   * @param contentType - Type of content
   * @param slug - Content identifier
   * @returns Promise resolving to availability summary
   */
  async getLanguageAvailabilitySummary(
    contentType: ContentType,
    slug: string
  ): Promise<{
    totalLanguages: number
    availableCount: number
    missingLanguages: SupportedLocale[]
    availableLanguages: SupportedLocale[]
  }> {
    const supportedLocales: SupportedLocale[] = ['en', 'zh']
    const availability = await contentQueries.checkContentAvailability(
      contentType, 
      slug, 
      supportedLocales
    )

    const availableLanguages = supportedLocales.filter(locale => availability[locale])
    const missingLanguages = supportedLocales.filter(locale => !availability[locale])

    return {
      totalLanguages: supportedLocales.length,
      availableCount: availableLanguages.length,
      missingLanguages,
      availableLanguages
    }
  }

  // ==========================================
  // Language Switching Logic
  // ==========================================

  /**
   * Handle content language switch
   * 
   * Implements intelligent language switching with fallback strategies.
   * This is the main function for handling language switch requests,
   * providing the best possible user experience.
   * 
   * @param params - Language switch parameters
   * @returns Promise resolving to language switch result
   */
  async handleContentLanguageSwitch(
    params: LanguageSwitchParams
  ): Promise<LanguageSwitchResult> {
    const { currentPath, currentLocale, targetLocale, baseUrl = '', context } = params

    // If switching to the same locale, return current path
    if (currentLocale === targetLocale) {
      return {
        url: currentPath,
        strategy: 'direct',
        available: true,
        metadata: {
          fallbackReason: 'Same locale requested'
        }
      }
    }

    // If context is provided (we know the content type and slug)
    if (context?.contentType && context?.slug) {
      return this.handleKnownContentSwitch(
        context.contentType as ContentType,
        context.slug,
        targetLocale,
        baseUrl
      )
    }

    // Try to detect content from the current path
    const detectedContent = this.detectContentFromPath(currentPath, currentLocale)

    if (detectedContent.isContentPage && detectedContent.slug) {
      return this.handleKnownContentSwitch(
        detectedContent.contentType,
        detectedContent.slug,
        targetLocale,
        baseUrl
      )
    }

    // For non-content pages, generate a simple locale switch
    return this.handleGenericLanguageSwitch(currentPath, currentLocale, targetLocale, baseUrl)
  }

  /**
   * Handle language switch for known content
   * 
   * @private
   * @param contentType - Content type
   * @param slug - Content slug
   * @param targetLocale - Target locale
   * @param baseUrl - Base URL for absolute URLs
   * @returns Promise resolving to language switch result
   */
  private async handleKnownContentSwitch(
    contentType: ContentType,
    slug: string,
    targetLocale: SupportedLocale,
    baseUrl: string
  ): Promise<LanguageSwitchResult> {
    try {
      // Check if content exists in target locale
      const contentExists = await contentQueries.contentExistsInLocale(
        contentType,
        slug,
        targetLocale
      )

      if (contentExists) {
        // Direct navigation to content in target locale
        const [targetTitle, originalTitle] = await Promise.all([
          contentQueries.getContentTitle(contentType, slug, targetLocale),
          contentQueries.getContentTitle(contentType, slug, 'en') // fallback to English for original
        ])

        return {
          url: generateContentUrl(contentType, slug, targetLocale, { absolute: true, baseUrl }),
          strategy: 'direct',
          available: true,
          metadata: {
            originalTitle: originalTitle || undefined,
            targetTitle: targetTitle || undefined
          }
        }
      } else {
        // Fallback to content list page in target locale
        return {
          url: generateContentListUrl(contentType, targetLocale, { absolute: true, baseUrl }),
          strategy: 'fallback',
          available: false,
          metadata: {
            fallbackReason: `Content "${slug}" not available in target language`
          }
        }
      }
    } catch (error) {
      console.error('Error handling content language switch:', error)
      
      // Fallback to homepage in target locale
      const homeUrl = targetLocale === 'en' ? baseUrl || '/' : `${baseUrl}/${targetLocale}`
      
      return {
        url: homeUrl,
        strategy: 'home',
        available: false,
        metadata: {
          fallbackReason: `Error occurred during language switch: ${error instanceof Error ? error.message : 'Unknown error'}`
        }
      }
    }
  }

  /**
   * Handle generic language switch for non-content pages
   * 
   * @private
   * @param currentPath - Current pathname
   * @param currentLocale - Current locale
   * @param targetLocale - Target locale
   * @param baseUrl - Base URL
   * @returns Language switch result
   */
  private handleGenericLanguageSwitch(
    currentPath: string,
    currentLocale: SupportedLocale,
    targetLocale: SupportedLocale,
    baseUrl: string
  ): LanguageSwitchResult {
    // Remove current locale prefix if present
    let cleanPath = currentPath
    if (currentLocale !== 'en' && currentPath.startsWith(`/${currentLocale}`)) {
      cleanPath = currentPath.replace(`/${currentLocale}`, '') || '/'
    }

    // Add target locale prefix if not default locale
    const targetPath = targetLocale === 'en' 
      ? cleanPath 
      : `/${targetLocale}${cleanPath}`

    return {
      url: `${baseUrl}${targetPath}`,
      strategy: 'direct',
      available: true,
      metadata: {
        fallbackReason: 'Generic path language switch'
      }
    }
  }

  /**
   * Detect content information from path
   * 
   * @private
   * @param pathname - URL pathname
   * @param locale - Current locale
   * @returns Content page information
   */
  private detectContentFromPath(pathname: string, locale: SupportedLocale): ContentPageInfo {
    // This would use the content detection utility
    // For now, return a basic implementation
    const segments = pathname.split('/').filter(Boolean)
    
    // Remove locale segment if present
    if (segments[0] === locale && locale !== 'en') {
      segments.shift()
    }

    // Check for content patterns
    if (segments[0] === 'blogs' && segments[1]) {
      return {
        contentType: 'blog',
        slug: segments[1],
        isContentPage: true,
        pathSegments: segments
      }
    }
    
    if (segments[0] === 'products' && segments[1]) {
      return {
        contentType: 'product',
        slug: segments[1],
        isContentPage: true,
        pathSegments: segments
      }
    }
    
    if (segments[0] === 'case-studies' && segments[1]) {
      return {
        contentType: 'case-study',
        slug: segments[1],
        isContentPage: true,
        pathSegments: segments
      }
    }

    return {
      contentType: 'blog', // default
      slug: '',
      isContentPage: false,
      pathSegments: segments
    }
  }

  // ==========================================
  // Utility Methods
  // ==========================================

  /**
   * Generate hreflang attributes for SEO
   * 
   * Generates hreflang attributes for all available language versions
   * of content, improving SEO for multilingual sites.
   * 
   * @param contentType - Content type
   * @param slug - Content slug
   * @param baseUrl - Base URL for absolute URLs
   * @returns Promise resolving to hreflang attributes
   */
  async generateHreflangAttributes(
    contentType: ContentType,
    slug: string,
    baseUrl: string = ''
  ): Promise<Array<{ hreflang: string; href: string }>> {
    const versions = await this.getAvailableLanguageVersions(contentType, slug)
    
    return versions
      .filter(version => version.available)
      .map(version => ({
        hreflang: version.lang,
        href: `${baseUrl}${version.url}`
      }))
  }

  /**
   * Get language switch analytics data
   * 
   * Provides data for analytics tracking of language switching behavior.
   * 
   * @param params - Language switch parameters
   * @returns Analytics data object
   */
  getLanguageSwitchAnalytics(params: LanguageSwitchParams): {
    event: string
    properties: Record<string, any>
  } {
    return {
      event: 'language_switch',
      properties: {
        from_locale: params.currentLocale,
        to_locale: params.targetLocale,
        content_type: params.context?.contentType,
        content_slug: params.context?.slug,
        current_path: params.currentPath
      }
    }
  }
}

// Create and export singleton instance
export const languageSwitching = new LanguageSwitchingService()

// Export convenience functions that delegate to the singleton with proper binding
export const getAvailableLanguageVersions = (
  contentType: ContentType,
  slug: string,
  currentLocale: SupportedLocale = 'en'
) => languageSwitching.getAvailableLanguageVersions.call(languageSwitching, contentType, slug, currentLocale)

export const getLanguageAvailabilitySummary = (
  contentType: ContentType,
  slug: string
) => languageSwitching.getLanguageAvailabilitySummary.call(languageSwitching, contentType, slug)

export const handleContentLanguageSwitch = (
  params: LanguageSwitchParams
) => languageSwitching.handleContentLanguageSwitch.call(languageSwitching, params)

export const generateHreflangAttributes = (
  contentType: ContentType,
  slug: string,
  baseUrl?: string
) => languageSwitching.generateHreflangAttributes.call(languageSwitching, contentType, slug, baseUrl)

export const getLanguageSwitchAnalytics = (
  params: LanguageSwitchParams
) => languageSwitching.getLanguageSwitchAnalytics.call(languageSwitching, params)
