/**
 * SEO Metadata Service - Business Logic Layer
 * 
 * This module provides comprehensive SEO metadata generation for content items.
 * It creates optimized metadata for search engines, social media platforms,
 * and other content consumers while following SEO best practices.
 * 
 * Key Features:
 * - Comprehensive SEO metadata generation
 * - Open Graph and Twitter Card support
 * - Automatic keyword extraction and optimization
 * - Canonical URL generation with language support
 * - Schema.org structured data generation
 * - Content-specific SEO optimizations
 * 
 * Architecture:
 * This service implements business logic for SEO optimization, coordinating
 * between content data and SEO best practices to generate optimal metadata
 * for search engine visibility and social media sharing.
 */

import { generateCanonicalUrl, generateAlternateUrls } from '../utils'
import type { 
  ContentItem, 
  ContentType, 
  SupportedLocale,
  SEOMetadata,
  BlogContent,
  ProductContent,
  CaseStudyContent
} from '../types'

/**
 * SEO Metadata Service Class
 * 
 * Provides comprehensive SEO metadata generation with content-specific
 * optimizations and best practices implementation.
 */
export class SEOMetadataService {

  // ==========================================
  // Core SEO Metadata Generation
  // ==========================================

  /**
   * Generate comprehensive SEO metadata for content
   * 
   * Creates complete SEO metadata including title, description, keywords,
   * Open Graph data, Twitter Cards, and canonical URLs. Optimized for
   * search engine visibility and social media sharing.
   * 
   * @param content - Content item to generate metadata for
   * @param options - Additional options for metadata generation
   * @returns Complete SEO metadata object
   */
  generateSEOMetadata(
    content: ContentItem,
    options: {
      baseUrl?: string
      availableLocales?: SupportedLocale[]
      siteName?: string
      defaultImage?: string
    } = {}
  ): SEOMetadata {
    const { 
      baseUrl = '', 
      availableLocales = [content.locale as SupportedLocale], 
      siteName = 'ShipAny',
      defaultImage = '/images/og-default.jpg'
    } = options

    // Generate base metadata
    const title = this.generateSEOTitle(content, siteName)
    const description = this.generateSEODescription(content)
    const keywords = this.generateSEOKeywords(content)
    const canonicalUrl = generateCanonicalUrl(
      content.type, 
      content.slug, 
      availableLocales, 
      { baseUrl }
    )

    // Determine the best image for social sharing
    const socialImage = content.image || defaultImage

    return {
      title,
      description,
      keywords,
      canonicalUrl,
      openGraph: {
        title: this.generateOpenGraphTitle(content, siteName),
        description: this.generateOpenGraphDescription(content),
        image: socialImage,
        type: this.getOpenGraphType(content.type)
      },
      twitterCard: {
        card: socialImage ? 'summary_large_image' : 'summary',
        title: this.generateTwitterTitle(content),
        description: this.generateTwitterDescription(content),
        image: socialImage
      }
    }
  }

  /**
   * Generate SEO metadata for content list pages
   * 
   * Creates SEO metadata optimized for content list pages like
   * blog index, product catalog, or case studies overview.
   * 
   * @param contentType - Type of content list
   * @param locale - Page locale
   * @param options - Additional options
   * @returns SEO metadata for list page
   */
  generateListPageSEOMetadata(
    contentType: ContentType,
    locale: SupportedLocale,
    options: {
      baseUrl?: string
      siteName?: string
      category?: string
      page?: number
    } = {}
  ): SEOMetadata {
    const { baseUrl = '', siteName = 'ShipAny', category, page = 1 } = options

    const typeLabels = {
      blog: 'Blog',
      product: 'Products',
      'case-study': 'Case Studies'
    }

    const typeLabel = typeLabels[contentType]
    const categoryText = category ? ` - ${category}` : ''
    const pageText = page > 1 ? ` - Page ${page}` : ''
    
    const title = `${typeLabel}${categoryText}${pageText} | ${siteName}`
    const description = this.generateListPageDescription(contentType, category)
    
    // Generate canonical URL for list page
    const canonicalUrl = `${baseUrl}/${contentType === 'case-study' ? 'case-studies' : `${contentType}s`}`

    return {
      title,
      description,
      keywords: this.generateListPageKeywords(contentType, category),
      canonicalUrl,
      openGraph: {
        title,
        description,
        type: 'website'
      },
      twitterCard: {
        card: 'summary',
        title,
        description
      }
    }
  }

  // ==========================================
  // Title Generation
  // ==========================================

  /**
   * Generate optimized SEO title
   * 
   * @private
   * @param content - Content item
   * @param siteName - Site name for branding
   * @returns Optimized SEO title
   */
  private generateSEOTitle(content: ContentItem, siteName: string): string {
    // Use custom SEO title if available
    if (content.seo?.title) {
      return content.seo.title
    }

    // Generate title based on content type
    const baseTitle = content.title
    const typeContext = this.getContentTypeContext(content.type)
    
    // Keep title under 60 characters for optimal SEO
    const maxLength = 60 - siteName.length - 3 // Account for " | " separator
    const truncatedTitle = baseTitle.length > maxLength 
      ? `${baseTitle.substring(0, maxLength - 3)}...`
      : baseTitle

    return `${truncatedTitle} | ${siteName}`
  }

  /**
   * Generate Open Graph title
   * 
   * @private
   * @param content - Content item
   * @param siteName - Site name
   * @returns Open Graph optimized title
   */
  private generateOpenGraphTitle(content: ContentItem, siteName: string): string {
    // Open Graph titles can be longer than SEO titles
    return content.seo?.title || `${content.title} | ${siteName}`
  }

  /**
   * Generate Twitter title
   * 
   * @private
   * @param content - Content item
   * @returns Twitter optimized title
   */
  private generateTwitterTitle(content: ContentItem): string {
    // Twitter titles should be concise
    const maxLength = 70
    return content.title.length > maxLength
      ? `${content.title.substring(0, maxLength - 3)}...`
      : content.title
  }

  // ==========================================
  // Description Generation
  // ==========================================

  /**
   * Generate SEO description
   * 
   * @private
   * @param content - Content item
   * @returns SEO optimized description
   */
  private generateSEODescription(content: ContentItem): string {
    // Use custom SEO description if available
    if (content.seo?.description) {
      return content.seo.description
    }

    // Use content description or generate from content
    let description = content.description

    // Add content-specific context
    const typeContext = this.getContentTypeContext(content.type)
    if (typeContext && !description.toLowerCase().includes(typeContext.toLowerCase())) {
      description = `${typeContext}: ${description}`
    }

    // Ensure description is within optimal length (150-160 characters)
    const maxLength = 160
    if (description.length > maxLength) {
      description = `${description.substring(0, maxLength - 3)}...`
    }

    return description
  }

  /**
   * Generate Open Graph description
   * 
   * @private
   * @param content - Content item
   * @returns Open Graph optimized description
   */
  private generateOpenGraphDescription(content: ContentItem): string {
    // Open Graph descriptions can be longer
    return content.seo?.description || content.description
  }

  /**
   * Generate Twitter description
   * 
   * @private
   * @param content - Content item
   * @returns Twitter optimized description
   */
  private generateTwitterDescription(content: ContentItem): string {
    // Twitter descriptions should be concise
    const description = content.seo?.description || content.description
    const maxLength = 200
    
    return description.length > maxLength
      ? `${description.substring(0, maxLength - 3)}...`
      : description
  }

  /**
   * Generate list page description
   * 
   * @private
   * @param contentType - Content type
   * @param category - Optional category
   * @returns List page description
   */
  private generateListPageDescription(contentType: ContentType, category?: string): string {
    const descriptions = {
      blog: 'Discover our latest blog posts, insights, and updates on technology, business, and innovation.',
      product: 'Explore our comprehensive product catalog with detailed specifications and features.',
      'case-study': 'Read our detailed case studies showcasing successful projects and client outcomes.'
    }

    let description = descriptions[contentType]
    
    if (category) {
      description = `${description.replace('our latest', `our latest ${category}`).replace('our comprehensive', `our ${category}`).replace('our detailed', `our ${category}`)}`
    }

    return description
  }

  // ==========================================
  // Keywords Generation
  // ==========================================

  /**
   * Generate SEO keywords
   * 
   * @private
   * @param content - Content item
   * @returns Array of SEO keywords
   */
  private generateSEOKeywords(content: ContentItem): string[] {
    const keywords: string[] = []

    // Use custom SEO keywords if available
    if (content.seo?.keywords) {
      keywords.push(...content.seo.keywords)
    }

    // Add content tags
    if (content.tags) {
      keywords.push(...content.tags)
    }

    // Add content type specific keywords
    keywords.push(...this.getContentTypeKeywords(content))

    // Remove duplicates and limit to 10 keywords
    return [...new Set(keywords)].slice(0, 10)
  }

  /**
   * Generate list page keywords
   * 
   * @private
   * @param contentType - Content type
   * @param category - Optional category
   * @returns Array of keywords for list page
   */
  private generateListPageKeywords(contentType: ContentType, category?: string): string[] {
    const baseKeywords = {
      blog: ['blog', 'articles', 'insights', 'news', 'updates'],
      product: ['products', 'catalog', 'solutions', 'services'],
      'case-study': ['case studies', 'success stories', 'projects', 'results']
    }

    const keywords = [...baseKeywords[contentType]]
    
    if (category) {
      keywords.push(category, `${category} ${contentType}`)
    }

    return keywords
  }

  // ==========================================
  // Helper Methods
  // ==========================================

  /**
   * Get content type context for SEO
   * 
   * @private
   * @param contentType - Content type
   * @returns Context string for the content type
   */
  private getContentTypeContext(contentType: ContentType): string {
    const contexts = {
      blog: 'Blog Post',
      product: 'Product',
      'case-study': 'Case Study'
    }
    
    return contexts[contentType] || ''
  }

  /**
   * Get content type specific keywords
   * 
   * @private
   * @param content - Content item
   * @returns Content type specific keywords
   */
  private getContentTypeKeywords(content: ContentItem): string[] {
    const baseKeywords = ['shipany']
    
    switch (content.type) {
      case 'blog':
        return [...baseKeywords, 'blog', 'article', 'insights']
      case 'product':
        return [...baseKeywords, 'product', 'solution', 'service']
      case 'case-study':
        return [...baseKeywords, 'case study', 'success story', 'project']
      default:
        return baseKeywords
    }
  }

  /**
   * Get Open Graph type for content
   * 
   * @private
   * @param contentType - Content type
   * @returns Open Graph type
   */
  private getOpenGraphType(contentType: ContentType): 'article' | 'website' {
    return contentType === 'blog' ? 'article' : 'website'
  }
}

// Create and export singleton instance
export const seoMetadata = new SEOMetadataService()

// Export convenience functions that delegate to the singleton
export const {
  generateSEOMetadata,
  generateListPageSEOMetadata
} = seoMetadata
