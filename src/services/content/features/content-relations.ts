/**
 * Content Relations Service - Business Logic Layer
 * 
 * This module provides functionality for discovering and managing relationships
 * between content items. It implements algorithms for finding related content,
 * content recommendations, and content discovery features.
 * 
 * Key Features:
 * - Related content discovery based on tags, categories, and content similarity
 * - Content recommendation algorithms
 * - Cross-content-type relationship discovery
 * - Content series and sequence management
 * - Popular and trending content identification
 * 
 * Architecture:
 * This service implements business logic for content relationships, using
 * various algorithms to discover meaningful connections between content items
 * for improved user experience and content discovery.
 */

import { contentQueries } from '../core'
import type { 
  ContentItem, 
  ContentType, 
  SupportedLocale,
  QueryOptions,
  BlogContent,
  ProductContent,
  CaseStudyContent
} from '../types'

/**
 * Content Relations Service Class
 * 
 * Provides comprehensive content relationship discovery and management
 * with multiple algorithms for finding related and recommended content.
 */
export class ContentRelationsService {

  // ==========================================
  // Related Content Discovery
  // ==========================================

  /**
   * Find related content items
   * 
   * Discovers content items related to the given content using multiple
   * relationship algorithms including tag similarity, category matching,
   * and content type relationships.
   * 
   * @param content - Source content item
   * @param options - Options for related content discovery
   * @returns Promise resolving to array of related content items
   */
  async findRelatedContent<T extends ContentItem>(
    content: ContentItem,
    options: {
      limit?: number
      sameType?: boolean
      includeOtherTypes?: boolean
      locale?: SupportedLocale
      excludeSelf?: boolean
    } = {}
  ): Promise<T[]> {
    const {
      limit = 5,
      sameType = true,
      includeOtherTypes = false,
      locale = content.locale as SupportedLocale,
      excludeSelf = true
    } = options

    try {
      const relatedItems: T[] = []

      // Find related content by tags
      if (content.tags && content.tags.length > 0) {
        const tagRelated = await this.findContentByTags<T>(
          content.tags,
          content.type,
          locale,
          { limit: limit * 2, excludeSlug: excludeSelf ? content.slug : undefined }
        )
        relatedItems.push(...tagRelated)
      }

      // Find related content by category (if applicable)
      const categoryRelated = await this.findContentByCategory<T>(
        content,
        locale,
        { limit: limit, excludeSlug: excludeSelf ? content.slug : undefined }
      )
      relatedItems.push(...categoryRelated)

      // Find related content from other types if enabled
      if (includeOtherTypes) {
        const crossTypeRelated = await this.findCrossTypeRelatedContent<T>(
          content,
          locale,
          { limit: Math.ceil(limit / 2) }
        )
        relatedItems.push(...crossTypeRelated)
      }

      // Remove duplicates and limit results
      const uniqueItems = this.removeDuplicateContent(relatedItems)
      const scoredItems = this.scoreContentRelevance(content, uniqueItems)
      
      return scoredItems.slice(0, limit)
    } catch (error) {
      console.error('Error finding related content:', error)
      return []
    }
  }

  /**
   * Find content by tags
   * 
   * @private
   * @param tags - Tags to match
   * @param contentType - Content type to search
   * @param locale - Content locale
   * @param options - Search options
   * @returns Promise resolving to matching content
   */
  private async findContentByTags<T extends ContentItem>(
    tags: string[],
    contentType: ContentType,
    locale: SupportedLocale,
    options: { limit?: number; excludeSlug?: string } = {}
  ): Promise<T[]> {
    const { limit = 10, excludeSlug } = options

    // Get all content of the same type
    const allContent = await contentQueries.getPublishedContentList<T>(
      contentType,
      locale,
      { limit: 100 } // Get a larger set to filter from
    )

    // Filter by tag overlap
    const taggedContent = allContent.filter(item => {
      if (excludeSlug && item.slug === excludeSlug) return false
      if (!item.tags || item.tags.length === 0) return false
      
      // Check for tag overlap
      const overlap = item.tags.filter(tag => tags.includes(tag))
      return overlap.length > 0
    })

    // Sort by tag overlap count
    taggedContent.sort((a, b) => {
      const aOverlap = a.tags?.filter(tag => tags.includes(tag)).length || 0
      const bOverlap = b.tags?.filter(tag => tags.includes(tag)).length || 0
      return bOverlap - aOverlap
    })

    return taggedContent.slice(0, limit)
  }

  /**
   * Find content by category
   * 
   * @private
   * @param content - Source content
   * @param locale - Content locale
   * @param options - Search options
   * @returns Promise resolving to category-related content
   */
  private async findContentByCategory<T extends ContentItem>(
    content: ContentItem,
    locale: SupportedLocale,
    options: { limit?: number; excludeSlug?: string } = {}
  ): Promise<T[]> {
    const { limit = 5, excludeSlug } = options

    // Extract category from content (type-specific)
    let category: string | undefined

    switch (content.type) {
      case 'blog':
        category = (content as BlogContent).category
        break
      case 'product':
        category = (content as ProductContent).category
        break
      // case-study doesn't have category in our current schema
    }

    if (!category) return []

    try {
      const categoryContent = await contentQueries.getContentByCategory<T>(
        content.type,
        locale,
        category,
        { limit: limit + 1 } // Get one extra to account for potential self-exclusion
      )

      // Filter out the source content if needed
      return categoryContent.filter(item => 
        !excludeSlug || item.slug !== excludeSlug
      ).slice(0, limit)
    } catch (error) {
      console.error('Error finding content by category:', error)
      return []
    }
  }

  /**
   * Find cross-type related content
   * 
   * @private
   * @param content - Source content
   * @param locale - Content locale
   * @param options - Search options
   * @returns Promise resolving to cross-type related content
   */
  private async findCrossTypeRelatedContent<T extends ContentItem>(
    content: ContentItem,
    locale: SupportedLocale,
    options: { limit?: number } = {}
  ): Promise<T[]> {
    const { limit = 3 } = options
    const otherTypes = (['blog', 'product', 'case-study'] as ContentType[])
      .filter(type => type !== content.type)

    const crossTypeContent: T[] = []

    for (const type of otherTypes) {
      if (content.tags && content.tags.length > 0) {
        const typeContent = await this.findContentByTags<T>(
          content.tags,
          type,
          locale,
          { limit: Math.ceil(limit / otherTypes.length) }
        )
        crossTypeContent.push(...typeContent)
      }
    }

    return crossTypeContent.slice(0, limit)
  }

  // ==========================================
  // Content Recommendations
  // ==========================================

  /**
   * Get content recommendations
   * 
   * Provides personalized content recommendations based on various factors
   * including popularity, recency, and content relationships.
   * 
   * @param options - Recommendation options
   * @returns Promise resolving to recommended content
   */
  async getContentRecommendations<T extends ContentItem>(
    options: {
      contentType?: ContentType
      locale?: SupportedLocale
      limit?: number
      strategy?: 'popular' | 'recent' | 'mixed'
      excludeSlugs?: string[]
    } = {}
  ): Promise<T[]> {
    const {
      contentType = 'blog',
      locale = 'en',
      limit = 6,
      strategy = 'mixed',
      excludeSlugs = []
    } = options

    try {
      switch (strategy) {
        case 'popular':
          return this.getPopularContent<T>(contentType, locale, limit, excludeSlugs)
        case 'recent':
          return this.getRecentContent<T>(contentType, locale, limit, excludeSlugs)
        case 'mixed':
        default:
          return this.getMixedRecommendations<T>(contentType, locale, limit, excludeSlugs)
      }
    } catch (error) {
      console.error('Error getting content recommendations:', error)
      return []
    }
  }

  /**
   * Get popular content
   * 
   * @private
   * @param contentType - Content type
   * @param locale - Content locale
   * @param limit - Number of items to return
   * @param excludeSlugs - Slugs to exclude
   * @returns Promise resolving to popular content
   */
  private async getPopularContent<T extends ContentItem>(
    contentType: ContentType,
    locale: SupportedLocale,
    limit: number,
    excludeSlugs: string[]
  ): Promise<T[]> {
    // For now, we'll use recent content as a proxy for popular
    // In a real implementation, this would use analytics data
    const content = await contentQueries.getRecentContent<T>(contentType, locale, limit * 2)
    
    return content
      .filter(item => !excludeSlugs.includes(item.slug))
      .slice(0, limit)
  }

  /**
   * Get recent content
   * 
   * @private
   * @param contentType - Content type
   * @param locale - Content locale
   * @param limit - Number of items to return
   * @param excludeSlugs - Slugs to exclude
   * @returns Promise resolving to recent content
   */
  private async getRecentContent<T extends ContentItem>(
    contentType: ContentType,
    locale: SupportedLocale,
    limit: number,
    excludeSlugs: string[]
  ): Promise<T[]> {
    const content = await contentQueries.getRecentContent<T>(contentType, locale, limit * 2)
    
    return content
      .filter(item => !excludeSlugs.includes(item.slug))
      .slice(0, limit)
  }

  /**
   * Get mixed recommendations
   * 
   * @private
   * @param contentType - Content type
   * @param locale - Content locale
   * @param limit - Number of items to return
   * @param excludeSlugs - Slugs to exclude
   * @returns Promise resolving to mixed recommendations
   */
  private async getMixedRecommendations<T extends ContentItem>(
    contentType: ContentType,
    locale: SupportedLocale,
    limit: number,
    excludeSlugs: string[]
  ): Promise<T[]> {
    // Mix of recent and popular content
    const recentLimit = Math.ceil(limit * 0.7)
    const popularLimit = limit - recentLimit

    const [recent, popular] = await Promise.all([
      this.getRecentContent<T>(contentType, locale, recentLimit, excludeSlugs),
      this.getPopularContent<T>(contentType, locale, popularLimit, excludeSlugs)
    ])

    // Combine and remove duplicates
    const combined = [...recent, ...popular]
    return this.removeDuplicateContent(combined).slice(0, limit)
  }

  // ==========================================
  // Utility Methods
  // ==========================================

  /**
   * Remove duplicate content items
   * 
   * @private
   * @param content - Array of content items
   * @returns Array with duplicates removed
   */
  private removeDuplicateContent<T extends ContentItem>(content: T[]): T[] {
    const seen = new Set<string>()
    return content.filter(item => {
      const key = `${item.type}-${item.slug}-${item.locale}`
      if (seen.has(key)) return false
      seen.add(key)
      return true
    })
  }

  /**
   * Score content relevance
   * 
   * @private
   * @param sourceContent - Source content for comparison
   * @param candidates - Candidate content items
   * @returns Sorted array by relevance score
   */
  private scoreContentRelevance<T extends ContentItem>(
    sourceContent: ContentItem,
    candidates: T[]
  ): T[] {
    return candidates
      .map(item => ({
        item,
        score: this.calculateRelevanceScore(sourceContent, item)
      }))
      .sort((a, b) => b.score - a.score)
      .map(scored => scored.item)
  }

  /**
   * Calculate relevance score between two content items
   * 
   * @private
   * @param source - Source content
   * @param candidate - Candidate content
   * @returns Relevance score (higher is more relevant)
   */
  private calculateRelevanceScore(source: ContentItem, candidate: ContentItem): number {
    let score = 0

    // Same type bonus
    if (source.type === candidate.type) {
      score += 10
    }

    // Tag overlap
    if (source.tags && candidate.tags) {
      const overlap = source.tags.filter(tag => candidate.tags!.includes(tag))
      score += overlap.length * 5
    }

    // Recency bonus (newer content gets higher score)
    const sourceDate = new Date(source.date).getTime()
    const candidateDate = new Date(candidate.date).getTime()
    const daysDiff = Math.abs(sourceDate - candidateDate) / (1000 * 60 * 60 * 24)
    
    if (daysDiff < 30) score += 5
    else if (daysDiff < 90) score += 3
    else if (daysDiff < 180) score += 1

    return score
  }
}

// Create and export singleton instance
export const contentRelations = new ContentRelationsService()

// Export convenience functions that delegate to the singleton
export const {
  findRelatedContent,
  getContentRecommendations
} = contentRelations
