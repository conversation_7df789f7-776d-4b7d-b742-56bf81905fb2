/**
 * Static Generation Support - Core Data Operations
 * 
 * This module provides specialized functions for Next.js static generation,
 * including support for generateStaticParams, getStaticProps, and build-time
 * content operations. It optimizes content retrieval for build performance.
 * 
 * Key Features:
 * - Optimized content retrieval for build-time operations
 * - Support for Next.js 13+ App Router static generation
 * - Efficient slug and locale combination generation
 * - Build-time content validation and error handling
 * - Memory-efficient content processing for large sites
 * 
 * Architecture:
 * This service is specifically designed for build-time operations and
 * includes optimizations that may not be suitable for runtime use.
 * It focuses on bulk operations and efficient data processing.
 */

import { cmsAdapter } from './cms-adapter'
import type { 
  ContentType, 
  ContentItem, 
  SupportedLocale,
  BlogContent,
  ProductContent,
  CaseStudyContent
} from '../types'

/**
 * Static Generation Service Class
 * 
 * Provides optimized content operations for Next.js static generation,
 * including support for App Router and Pages Router patterns.
 */
export class StaticGenerationService {

  // ==========================================
  // Next.js App Router Support
  // ==========================================

  /**
   * Generate static params for App Router
   * 
   * Generates the parameter combinations needed for Next.js 13+ App Router
   * generateStaticParams function. This creates all possible slug/locale
   * combinations for static page generation.
   * 
   * Usage in page.tsx:
   * ```typescript
   * export async function generateStaticParams() {
   *   return await staticGeneration.generateStaticParams('blog')
   * }
   * ```
   * 
   * @param type - Content type to generate params for
   * @returns Promise resolving to array of param objects
   */
  async generateStaticParams(
    type: ContentType
  ): Promise<Array<{ locale: string; slug: string }>> {
    try {
      const slugs = await cmsAdapter.getAllContentSlugs(type)
      
      // Convert to the format expected by Next.js generateStaticParams
      return slugs.map(({ locale, slug }) => ({
        locale: locale.toString(),
        slug
      }))
    } catch (error) {
      console.error(`Error generating static params for ${type}:`, error)
      return []
    }
  }

  /**
   * Generate static params for all content types
   * 
   * Generates static params for all supported content types,
   * useful for dynamic routing that handles multiple content types.
   * 
   * @returns Promise resolving to map of content type to params
   */
  async generateAllStaticParams(): Promise<Record<ContentType, Array<{ locale: string; slug: string }>>> {
    const contentTypes: ContentType[] = ['blog', 'product', 'case-study']
    const results = {} as Record<ContentType, Array<{ locale: string; slug: string }>>
    
    // Generate params for all content types in parallel
    const generations = contentTypes.map(async (type) => {
      const params = await this.generateStaticParams(type)
      results[type] = params
    })
    
    await Promise.all(generations)
    return results
  }

  // ==========================================
  // Content Retrieval for Static Generation
  // ==========================================

  /**
   * Get all content for static generation
   * 
   * Retrieves all content items of a specific type for build-time processing.
   * This includes all locales and all published states for maximum flexibility.
   * 
   * @param type - Content type to retrieve
   * @returns Promise resolving to all content items
   */
  async getAllContent<T extends ContentItem>(
    type: ContentType
  ): Promise<T[]> {
    try {
      return await cmsAdapter.getContentForStaticGeneration<T>(type)
    } catch (error) {
      console.error(`Error retrieving all content for ${type}:`, error)
      return []
    }
  }

  /**
   * Get content by locale for static generation
   * 
   * Retrieves all content items of a specific type in a specific locale,
   * useful for locale-specific static generation.
   * 
   * @param type - Content type
   * @param locale - Target locale
   * @returns Promise resolving to content items in the specified locale
   */
  async getContentByLocale<T extends ContentItem>(
    type: ContentType,
    locale: SupportedLocale
  ): Promise<T[]> {
    try {
      const allContent = await this.getAllContent<T>(type)
      return allContent.filter(item => item.locale === locale)
    } catch (error) {
      console.error(`Error retrieving content for ${type}/${locale}:`, error)
      return []
    }
  }

  /**
   * Get published content for static generation
   * 
   * Retrieves only published content items for production builds,
   * filtering out draft content that shouldn't be publicly accessible.
   * 
   * @param type - Content type
   * @param locale - Target locale (optional, returns all locales if not specified)
   * @returns Promise resolving to published content items
   */
  async getPublishedContent<T extends ContentItem>(
    type: ContentType,
    locale?: SupportedLocale
  ): Promise<T[]> {
    try {
      const allContent = await this.getAllContent<T>(type)
      let filteredContent = allContent.filter(item => item.published)
      
      if (locale) {
        filteredContent = filteredContent.filter(item => item.locale === locale)
      }
      
      return filteredContent
    } catch (error) {
      console.error(`Error retrieving published content for ${type}:`, error)
      return []
    }
  }

  // ==========================================
  // Build-time Utilities
  // ==========================================

  /**
   * Get content statistics for build reporting
   * 
   * Generates statistics about content for build-time reporting,
   * useful for monitoring content health and build performance.
   * 
   * @returns Promise resolving to content statistics
   */
  async getContentStatistics(): Promise<{
    total: number
    byType: Record<ContentType, number>
    byLocale: Record<SupportedLocale, number>
    published: number
    draft: number
  }> {
    try {
      const contentTypes: ContentType[] = ['blog', 'product', 'case-study']
      const locales: SupportedLocale[] = ['en', 'zh']
      
      let total = 0
      let published = 0
      let draft = 0
      const byType = {} as Record<ContentType, number>
      const byLocale = {} as Record<SupportedLocale, number>
      
      // Initialize counters
      contentTypes.forEach(type => { byType[type] = 0 })
      locales.forEach(locale => { byLocale[locale] = 0 })
      
      // Count content for each type
      for (const type of contentTypes) {
        const content = await this.getAllContent(type)
        byType[type] = content.length
        total += content.length
        
        // Count published vs draft
        content.forEach(item => {
          if (item.published) {
            published++
          } else {
            draft++
          }
          
          // Count by locale
          if (locales.includes(item.locale as SupportedLocale)) {
            byLocale[item.locale as SupportedLocale]++
          }
        })
      }
      
      return {
        total,
        byType,
        byLocale,
        published,
        draft
      }
    } catch (error) {
      console.error('Error generating content statistics:', error)
      return {
        total: 0,
        byType: { blog: 0, product: 0, 'case-study': 0 },
        byLocale: { en: 0, zh: 0 },
        published: 0,
        draft: 0
      }
    }
  }

  /**
   * Validate content for build
   * 
   * Performs validation checks on all content to ensure build quality,
   * identifying potential issues before deployment.
   * 
   * @returns Promise resolving to validation results
   */
  async validateContentForBuild(): Promise<{
    valid: boolean
    errors: Array<{ type: ContentType; slug: string; locale: string; error: string }>
    warnings: Array<{ type: ContentType; slug: string; locale: string; warning: string }>
  }> {
    const errors: Array<{ type: ContentType; slug: string; locale: string; error: string }> = []
    const warnings: Array<{ type: ContentType; slug: string; locale: string; warning: string }> = []
    
    try {
      const contentTypes: ContentType[] = ['blog', 'product', 'case-study']
      
      for (const type of contentTypes) {
        const content = await this.getAllContent(type)
        
        content.forEach(item => {
          // Check required fields
          if (!item.title) {
            errors.push({
              type,
              slug: item.slug,
              locale: item.locale,
              error: 'Missing title'
            })
          }
          
          if (!item.description) {
            warnings.push({
              type,
              slug: item.slug,
              locale: item.locale,
              warning: 'Missing description (recommended for SEO)'
            })
          }
          
          if (!item.date) {
            errors.push({
              type,
              slug: item.slug,
              locale: item.locale,
              error: 'Missing date'
            })
          }
          
          // Check slug format
          if (!/^[a-z0-9-]+$/.test(item.slug)) {
            errors.push({
              type,
              slug: item.slug,
              locale: item.locale,
              error: 'Invalid slug format'
            })
          }
        })
      }
    } catch (error) {
      console.error('Error validating content for build:', error)
      errors.push({
        type: 'blog',
        slug: 'unknown',
        locale: 'unknown',
        error: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    }
    
    return {
      valid: errors.length === 0,
      errors,
      warnings
    }
  }
}

// Create and export singleton instance
export const staticGeneration = new StaticGenerationService()

// Export convenience functions that delegate to the singleton
export const {
  generateStaticParams,
  generateAllStaticParams,
  getAllContent,
  getContentByLocale,
  getPublishedContent,
  getContentStatistics,
  validateContentForBuild
} = staticGeneration
