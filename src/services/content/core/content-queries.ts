/**
 * Content Queries Service - Core Data Operations
 * 
 * This module provides high-level content query operations that build upon
 * the CMS adapter. It offers convenient methods for common content operations
 * and implements business logic for content retrieval.
 * 
 * Key Features:
 * - High-level content query operations
 * - Business logic for content filtering and sorting
 * - Error handling and fallback strategies
 * - Performance optimization for common queries
 * - Type-safe content operations
 * 
 * Architecture:
 * This service sits between the CMS adapter (data layer) and the business
 * logic layer, providing convenient methods that combine multiple data
 * operations into useful business operations.
 */

import { cmsAdapter } from './cms-adapter'
import type { 
  ContentType, 
  ContentItem, 
  QueryOptions,
  SupportedLocale,
  BlogContent,
  ProductContent,
  CaseStudyContent,
  ServiceResult
} from '../types'

/**
 * Content Queries Service Class
 * 
 * Provides high-level content query operations with business logic,
 * error handling, and performance optimizations.
 */
export class ContentQueriesService {
  
  // ==========================================
  // Content Existence Checks
  // ==========================================

  /**
   * Check if content exists in a specific language
   * 
   * This function queries the content system to determine if a specific
   * piece of content (identified by type and slug) has a translation
   * available in the target language.
   * 
   * @param contentType - Type of content to check
   * @param slug - Unique identifier for the content
   * @param locale - Target locale to check
   * @returns Promise resolving to boolean indicating existence
   */
  async contentExistsInLocale(
    contentType: ContentType,
    slug: string,
    locale: SupportedLocale
  ): Promise<boolean> {
    // Early validation
    if (!slug || !contentType) {
      return false
    }

    try {
      return await cmsAdapter.contentExists(contentType, slug, locale)
    } catch (error) {
      console.error(`Error checking content existence: ${contentType}/${locale}/${slug}`, error)
      return false
    }
  }

  /**
   * Check content availability across multiple locales
   * 
   * Efficiently checks if content exists in multiple locales,
   * useful for language switching UI and content availability indicators.
   * 
   * @param contentType - Type of content
   * @param slug - Content slug
   * @param locales - Array of locales to check
   * @returns Promise resolving to map of locale to availability
   */
  async checkContentAvailability(
    contentType: ContentType,
    slug: string,
    locales: SupportedLocale[]
  ): Promise<Record<SupportedLocale, boolean>> {
    const availability = {} as Record<SupportedLocale, boolean>
    
    // Check all locales in parallel for better performance
    const checks = locales.map(async (locale) => {
      const exists = await this.contentExistsInLocale(contentType, slug, locale)
      availability[locale] = exists
    })
    
    await Promise.all(checks)
    return availability
  }

  // ==========================================
  // Content Metadata Operations
  // ==========================================

  /**
   * Get content title for display purposes
   * 
   * Retrieves the title of a content item in a specific language
   * for display in UI components. Includes fallback logic for
   * better user experience.
   * 
   * @param contentType - Type of content
   * @param slug - Unique identifier for the content
   * @param locale - Language locale for the content
   * @param fallbackLocale - Fallback locale if content not found
   * @returns Promise resolving to content title or null
   */
  async getContentTitle(
    contentType: ContentType,
    slug: string,
    locale: SupportedLocale,
    fallbackLocale: SupportedLocale = 'en'
  ): Promise<string | null> {
    // Early validation
    if (!slug || !contentType) {
      return null
    }

    try {
      // Try to get title in requested locale
      let title = await cmsAdapter.getContentTitle(contentType, slug, locale)
      
      // If not found and fallback is different, try fallback locale
      if (!title && fallbackLocale !== locale) {
        title = await cmsAdapter.getContentTitle(contentType, slug, fallbackLocale)
      }
      
      return title
    } catch (error) {
      console.error(`Error fetching content title: ${contentType}/${locale}/${slug}`, error)
      return null
    }
  }

  /**
   * Get content titles for multiple items
   * 
   * Efficiently retrieves titles for multiple content items,
   * useful for building navigation or content lists.
   * 
   * @param items - Array of content identifiers
   * @param locale - Target locale
   * @returns Promise resolving to map of slug to title
   */
  async getContentTitles(
    items: Array<{ type: ContentType; slug: string }>,
    locale: SupportedLocale
  ): Promise<Record<string, string | null>> {
    const titles = {} as Record<string, string | null>
    
    // Fetch all titles in parallel
    const fetches = items.map(async (item) => {
      const title = await this.getContentTitle(item.type, item.slug, locale)
      titles[item.slug] = title
    })
    
    await Promise.all(fetches)
    return titles
  }

  // ==========================================
  // Content List Operations
  // ==========================================

  /**
   * Get published content list
   * 
   * Retrieves a list of published content items with filtering
   * and sorting options. Automatically filters out unpublished content.
   * 
   * @param type - Content type
   * @param locale - Content locale
   * @param options - Query options
   * @returns Promise resolving to array of published content items
   */
  async getPublishedContentList<T extends ContentItem>(
    type: ContentType,
    locale: SupportedLocale,
    options: QueryOptions = {}
  ): Promise<T[]> {
    try {
      // Ensure we only get published content
      const queryOptions: QueryOptions = {
        ...options,
        published: true
      }
      
      return await cmsAdapter.getContentList<T>(type, locale, queryOptions)
    } catch (error) {
      console.error(`Error fetching published content list: ${type}/${locale}`, error)
      return []
    }
  }

  /**
   * Get recent content
   * 
   * Retrieves the most recently published content items,
   * useful for homepage feeds and "latest" sections.
   * 
   * @param type - Content type
   * @param locale - Content locale
   * @param limit - Maximum number of items to return
   * @returns Promise resolving to array of recent content items
   */
  async getRecentContent<T extends ContentItem>(
    type: ContentType,
    locale: SupportedLocale,
    limit: number = 5
  ): Promise<T[]> {
    return this.getPublishedContentList<T>(type, locale, {
      limit,
      sortBy: 'date',
      sortOrder: 'desc'
    })
  }

  /**
   * Get content by category
   * 
   * Retrieves content items filtered by category,
   * useful for category-specific pages and filtering.
   * 
   * @param type - Content type
   * @param locale - Content locale
   * @param category - Category to filter by
   * @param options - Additional query options
   * @returns Promise resolving to array of categorized content items
   */
  async getContentByCategory<T extends ContentItem>(
    type: ContentType,
    locale: SupportedLocale,
    category: string,
    options: QueryOptions = {}
  ): Promise<T[]> {
    return this.getPublishedContentList<T>(type, locale, {
      ...options,
      category
    })
  }

  /**
   * Search content
   * 
   * Performs a search across content items based on a query string,
   * searching in titles, descriptions, and content body.
   * 
   * @param type - Content type
   * @param locale - Content locale
   * @param query - Search query string
   * @param options - Additional query options
   * @returns Promise resolving to array of matching content items
   */
  async searchContent<T extends ContentItem>(
    type: ContentType,
    locale: SupportedLocale,
    query: string,
    options: QueryOptions = {}
  ): Promise<T[]> {
    if (!query.trim()) {
      return []
    }

    return this.getPublishedContentList<T>(type, locale, {
      ...options,
      search: query.trim()
    })
  }

  // ==========================================
  // Static Generation Support
  // ==========================================

  /**
   * Get all content for static generation
   * 
   * Retrieves all content items for build-time static generation,
   * including all locales and published status.
   * 
   * @param type - Content type
   * @returns Promise resolving to all content items
   */
  async getAllContentForStaticGeneration<T extends ContentItem>(
    type: ContentType
  ): Promise<T[]> {
    try {
      return await cmsAdapter.getContentForStaticGeneration<T>(type)
    } catch (error) {
      console.error(`Error fetching content for static generation: ${type}`, error)
      return []
    }
  }

  /**
   * Get content slugs for static params
   * 
   * Retrieves all slug and locale combinations for Next.js
   * generateStaticParams function.
   * 
   * @param type - Content type
   * @returns Promise resolving to array of slug/locale combinations
   */
  async getContentSlugsForStaticParams(
    type: ContentType
  ): Promise<Array<{ locale: SupportedLocale; slug: string }>> {
    try {
      return await cmsAdapter.getAllContentSlugs(type)
    } catch (error) {
      console.error(`Error fetching content slugs for static params: ${type}`, error)
      return []
    }
  }
}

// Create and export singleton instance
export const contentQueries = new ContentQueriesService()

// Export convenience functions that delegate to the singleton with proper binding
export const contentExistsInLocale = (
  contentType: ContentType,
  slug: string,
  locale: SupportedLocale
) => contentQueries.contentExistsInLocale.call(contentQueries, contentType, slug, locale)

export const checkContentAvailability = (
  contentType: ContentType,
  slug: string,
  locales: SupportedLocale[]
) => contentQueries.checkContentAvailability.call(contentQueries, contentType, slug, locales)

export const getContentTitle = (
  contentType: ContentType,
  slug: string,
  locale: SupportedLocale,
  fallbackLocale?: SupportedLocale
) => contentQueries.getContentTitle.call(contentQueries, contentType, slug, locale, fallbackLocale)

export const getContentTitles = (
  items: Array<{ type: ContentType; slug: string }>,
  locale: SupportedLocale
) => contentQueries.getContentTitles.call(contentQueries, items, locale)

export const getPublishedContentList = (
  contentType: ContentType,
  locale: SupportedLocale,
  options?: any
) => contentQueries.getPublishedContentList.call(contentQueries, contentType, locale, options)

export const getRecentContent = (
  contentType: ContentType,
  locale: SupportedLocale,
  limit?: number
) => contentQueries.getRecentContent.call(contentQueries, contentType, locale, limit)

export const getContentByCategory = (
  contentType: ContentType,
  category: string,
  locale: SupportedLocale
) => contentQueries.getContentByCategory.call(contentQueries, contentType, locale, category)

export const searchContent = (
  contentType: ContentType,
  query: string,
  locale: SupportedLocale
) => contentQueries.searchContent.call(contentQueries, contentType, locale, query)

export const getAllContentForStaticGeneration = (
  contentType: ContentType
) => contentQueries.getAllContentForStaticGeneration.call(contentQueries, contentType)

export const getContentSlugsForStaticParams = (
  contentType: ContentType
) => contentQueries.getContentSlugsForStaticParams.call(contentQueries, contentType)
