/**
 * CMS Adapter - Core Data Layer
 * 
 * This module provides the main interface to the CMS abstraction layer.
 * It serves as the primary data access point for content operations,
 * coordinating between the CMS backend and the application layer.
 * 
 * Key Features:
 * - Provider-agnostic content operations
 * - Static generation support for Next.js
 * - Automatic initialization and lifecycle management
 * - Type-safe content operations
 * - Performance optimization for build-time operations
 * 
 * Architecture:
 * This adapter implements the Repository pattern, providing a clean
 * interface to content data while abstracting the underlying CMS
 * implementation details.
 */

import { cms, initializeCMS } from '@/cms/services'
import type {
  ContentType,
  ContentItem,
  QueryOptions,
  BlogContent,
  ProductContent,
  CaseStudyContent,
  SupportedLocale,
  IContentService,
  ContentServiceConfig,
  ServiceResult
} from '../types'
import type { QueryOptions as CMSQueryOptions } from '@/cms/types/provider'

/**
 * CMS Adapter Service Class
 * 
 * Main service class that provides all content data access functionality
 * through a single, consistent interface. This class coordinates between
 * the CMS abstraction layer and the application layer.
 */
export class CMSAdapterService implements IContentService {
  private initialized = false
  private config: ContentServiceConfig | null = null

  /**
   * Initialize the CMS adapter service
   * 
   * Sets up the CMS backend and prepares the service for content operations.
   * This method should be called once during application startup.
   * 
   * @param config - Service configuration options
   */
  async initialize(config?: Partial<ContentServiceConfig>): Promise<void> {
    if (this.initialized) return

    // Default configuration
    const defaultConfig: ContentServiceConfig = {
      provider: {
        type: 'contentlayer2',
        options: {}
      },
      contentTypes: ['blog', 'product', 'case-study'],
      i18n: {
        defaultLocale: 'en',
        supportedLocales: ['en', 'zh'],
        languageSwitching: true
      },
      features: {
        seo: true,
        relatedContent: true,
        validation: true,
        staticGeneration: true
      },
      performance: {
        preloading: false,
        pageSize: 10,
        compression: false
      }
    }

    // Merge with provided configuration
    this.config = { ...defaultConfig, ...config }

    // Initialize the underlying CMS
    await initializeCMS({
      provider: this.config.provider.type,
      contentTypes: this.config.contentTypes,
      defaultLocale: this.config.i18n.defaultLocale,
      supportedLocales: this.config.i18n.supportedLocales,
      features: {
        seo: this.config.features.seo,
        relatedContent: this.config.features.relatedContent,
        languageSwitching: this.config.i18n.languageSwitching
      }
    })

    this.initialized = true
  }

  /**
   * Ensure service is initialized
   * 
   * @private
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initialize()
    }
  }

  // ==========================================
  // Static Generation Support
  // ==========================================

  /**
   * Get content for static generation
   * 
   * Retrieves all content items of a specific type for Next.js static generation.
   * This method is optimized for build-time usage and includes all locales.
   * 
   * @param type - Content type to retrieve
   * @returns Promise resolving to array of content items
   */
  async getContentForStaticGeneration<T extends ContentItem>(
    type: ContentType
  ): Promise<T[]> {
    await this.ensureInitialized()
    const result = await cms.getContentForStaticGeneration<any>(type)
    return result as T[]
  }

  /**
   * Get all content slugs for static generation
   * 
   * Retrieves all slug and locale combinations for generateStaticParams.
   * This is used by Next.js to pre-generate static pages at build time.
   * 
   * @param type - Content type to get slugs for
   * @returns Promise resolving to array of slug/locale combinations
   */
  async getAllContentSlugs(
    type: ContentType
  ): Promise<Array<{ locale: SupportedLocale; slug: string }>> {
    await this.ensureInitialized()
    const result = await cms.getAllContentSlugs(type)
    return result.map(item => ({
      ...item,
      locale: item.locale as SupportedLocale
    }))
  }

  // ==========================================
  // Content Queries
  // ==========================================

  /**
   * Get single content item
   * 
   * Retrieves a specific content item by type, slug, and locale.
   * Returns null if the content item is not found.
   * 
   * @param type - Content type
   * @param slug - Content slug identifier
   * @param locale - Content locale
   * @returns Promise resolving to content item or null
   */
  async getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: SupportedLocale
  ): Promise<T | null> {
    await this.ensureInitialized()
    const result = await cms.getContent<any>(type, slug, locale)
    return result as T | null
  }

  /**
   * Get content list with filtering and sorting
   * 
   * Retrieves a list of content items with optional filtering, sorting,
   * and pagination. Useful for content list pages and feeds.
   * 
   * @param type - Content type
   * @param locale - Content locale
   * @param options - Query options for filtering and sorting
   * @returns Promise resolving to array of content items
   */
  async getContentList<T extends ContentItem>(
    type: ContentType,
    locale: SupportedLocale,
    options?: QueryOptions
  ): Promise<T[]> {
    await this.ensureInitialized()

    // Convert QueryOptions to CMS provider format
    const cmsOptions: CMSQueryOptions | undefined = options ? {
      featured: options.published,
      tags: options.tags,
      author: undefined,
      sortBy: options.sortBy === 'date' ? 'publishedAt' as const :
              options.sortBy === 'title' ? 'title' as const :
              options.sortBy === 'slug' ? 'createdAt' as const : undefined,
      order: options.sortOrder,
      limit: options.limit,
      offset: options.offset
    } : undefined

    const result = await cms.getContentList<any>(type, locale, cmsOptions)
    return result as T[]
  }

  /**
   * Check if content exists in a specific locale
   * 
   * Efficiently checks if a content item exists without loading
   * the full content. Useful for language switching logic.
   * 
   * @param type - Content type
   * @param slug - Content slug
   * @param locale - Target locale
   * @returns Promise resolving to boolean indicating existence
   */
  async contentExists(
    type: ContentType,
    slug: string,
    locale: SupportedLocale
  ): Promise<boolean> {
    await this.ensureInitialized()
    return cms.contentExists(type, slug, locale)
  }

  /**
   * Get content title without loading full content
   * 
   * Retrieves just the title of a content item for efficient
   * operations like navigation or language switching.
   * 
   * @param type - Content type
   * @param slug - Content slug
   * @param locale - Content locale
   * @returns Promise resolving to content title or null
   */
  async getContentTitle(
    type: ContentType,
    slug: string,
    locale: SupportedLocale
  ): Promise<string | null> {
    await this.ensureInitialized()
    return cms.getContentTitle(type, slug, locale)
  }

  /**
   * Get available language versions of content
   * 
   * Retrieves information about which languages a specific
   * content item is available in. Used for language switching UI.
   * 
   * @param type - Content type
   * @param slug - Content slug
   * @returns Promise resolving to array of language versions
   */
  async getAvailableLanguages(
    type: ContentType,
    slug: string
  ): Promise<Array<{ lang: SupportedLocale; title: string; url: string; available: boolean }>> {
    await this.ensureInitialized()
    const result = await cms.getAvailableLanguages(type, slug)
    return result.map(item => ({
      ...item,
      lang: item.lang as SupportedLocale
    }))
  }

  // ==========================================
  // Service Management
  // ==========================================

  /**
   * Get service configuration
   * 
   * Returns the current service configuration for debugging
   * or runtime inspection.
   * 
   * @returns Current service configuration
   */
  getConfig(): ContentServiceConfig | null {
    return this.config
  }

  /**
   * Check if service is initialized
   * 
   * @returns True if the service is initialized and ready
   */
  isInitialized(): boolean {
    return this.initialized
  }

  /**
   * Reset service state
   * 
   * Resets the service to uninitialized state. Useful for testing
   * or when configuration needs to be changed.
   */
  reset(): void {
    this.initialized = false
    this.config = null
  }
}

// Create and export singleton instance
export const cmsAdapter = new CMSAdapterService()

// Export convenience functions that delegate to the singleton
export const getContentForStaticGeneration = <T extends ContentItem>(type: ContentType) =>
  cmsAdapter.getContentForStaticGeneration<T>(type)

export const getAllContentSlugs = (type: ContentType) =>
  cmsAdapter.getAllContentSlugs(type)

export const getContent = <T extends ContentItem>(type: ContentType, slug: string, locale: SupportedLocale) =>
  cmsAdapter.getContent<T>(type, slug, locale)

export const getContentList = <T extends ContentItem>(type: ContentType, locale: SupportedLocale, options?: QueryOptions) =>
  cmsAdapter.getContentList<T>(type, locale, options)

export const contentExists = (type: ContentType, slug: string, locale: SupportedLocale) =>
  cmsAdapter.contentExists(type, slug, locale)

export const getContentTitle = (type: ContentType, slug: string, locale: SupportedLocale) =>
  cmsAdapter.getContentTitle(type, slug, locale)

export const getAvailableLanguages = (type: ContentType, slug: string) =>
  cmsAdapter.getAvailableLanguages(type, slug)

// Export types for external use
export type {
  ContentType,
  ContentItem,
  BlogContent,
  ProductContent,
  CaseStudyContent,
  QueryOptions,
  SupportedLocale
}
