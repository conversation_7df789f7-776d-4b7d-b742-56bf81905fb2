/**
 * Content Core Services - Unified Export
 * 
 * This module provides a unified interface to all core content services.
 * The core layer handles direct data access and fundamental content operations,
 * serving as the foundation for higher-level business logic.
 * 
 * Core Services:
 * - CMS Adapter: Direct interface to the content management system
 * - Content Queries: High-level content query operations
 * - Static Generation: Build-time content operations for Next.js
 * 
 * Usage:
 * ```typescript
 * import { ContentService, contentQueries, staticGeneration } from '@/services/content/core'
 * ```
 * 
 * Architecture:
 * The core layer implements the Repository pattern, providing clean interfaces
 * to content data while abstracting the underlying CMS implementation details.
 * This layer focuses on data access and basic content operations.
 */

// CMS Adapter - Primary data access interface
export {
  cmsAdapter,
  getContentForStaticGeneration,
  getAllContentSlugs,
  getContent,
  getContentList,
  contentExists,
  getContentTitle,
  getAvailableLanguages
} from './cms-adapter'

// Content Queries - High-level query operations
export {
  contentQueries,
  contentExistsInLocale,
  checkContentAvailability,
  getContentTitles,
  getPublishedContentList,
  getRecentContent,
  getContentByCategory,
  searchContent,
  getAllContentForStaticGeneration,
  getContentSlugsForStaticParams
} from './content-queries'

// Static Generation - Build-time operations
export {
  staticGeneration,
  generateStaticParams,
  generateAllStaticParams,
  getAllContent,
  getContentByLocale,
  getPublishedContent,
  getContentStatistics,
  validateContentForBuild
} from './static-generation'

// Re-export types for convenience
export type {
  ContentType,
  ContentItem,
  BlogContent,
  ProductContent,
  CaseStudyContent,
  QueryOptions,
  SupportedLocale,
  IContentService,
  ContentServiceConfig,
  ServiceResult
} from '../types'

// Import instances for ContentService
import { cmsAdapter } from './cms-adapter'
import { contentQueries } from './content-queries'
import { staticGeneration } from './static-generation'
import type { ContentItem, ContentType, SupportedLocale, QueryOptions } from '../types'

/**
 * Main Content Service Interface
 *
 * Provides a unified interface to all core content operations.
 * This is the primary service that applications should use for
 * content operations, combining all core services into one interface.
 */
export class ContentService {
  /**
   * CMS Adapter instance for direct data access
   */
  readonly adapter = cmsAdapter

  /**
   * Content Queries instance for high-level operations
   */
  readonly queries = contentQueries

  /**
   * Static Generation instance for build-time operations
   */
  readonly static = staticGeneration

  /**
   * Initialize all core services
   * 
   * @param config - Optional service configuration
   */
  async initialize(config?: any): Promise<void> {
    await this.adapter.initialize(config)
  }

  /**
   * Check if services are initialized
   */
  isInitialized(): boolean {
    return this.adapter.isInitialized()
  }

  /**
   * Reset all services (useful for testing)
   */
  reset(): void {
    this.adapter.reset()
  }

  // ==========================================
  // Convenience Methods - Most Common Operations
  // ==========================================

  /**
   * Get single content item (most common operation)
   */
  async getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: SupportedLocale
  ): Promise<T | null> {
    return this.adapter.getContent<T>(type, slug, locale)
  }

  /**
   * Get content list (second most common operation)
   */
  async getContentList<T extends ContentItem>(
    type: ContentType,
    locale: SupportedLocale,
    options?: any
  ): Promise<T[]> {
    return this.queries.getPublishedContentList<T>(type, locale, options)
  }

  /**
   * Check content existence (common for language switching)
   */
  async contentExists(
    type: ContentType,
    slug: string,
    locale: SupportedLocale
  ): Promise<boolean> {
    return this.queries.contentExistsInLocale(type, slug, locale)
  }

  /**
   * Generate static params (common for Next.js)
   */
  async generateStaticParams(type: ContentType) {
    return this.static.generateStaticParams(type)
  }

  /**
   * Get all content for static generation (common for builds)
   */
  async getAllContent<T extends ContentItem>(type: ContentType): Promise<T[]> {
    return this.static.getAllContent<T>(type)
  }
}

// Create and export singleton instance
export const contentService = new ContentService()

// Export default as the main service
export default contentService

/**
 * Content Service Factory
 * 
 * Provides a factory function for creating content service instances
 * with custom configurations. Useful for testing or when multiple
 * service instances with different configurations are needed.
 */
export function createContentService(config?: any): ContentService {
  const service = new ContentService()
  
  // Initialize with custom configuration if provided
  if (config) {
    service.initialize(config).catch(error => {
      console.error('Failed to initialize content service:', error)
    })
  }
  
  return service
}

/**
 * Content Service Configuration Helper
 *
 * Provides helper functions for common service configurations.
 */
export const ContentServiceConfigHelper = {
  /**
   * Development configuration
   */
  development: {
    provider: {
      type: 'contentlayer2' as const,
      options: {}
    },
    contentTypes: ['blog', 'product', 'case-study'] as ContentType[],
    i18n: {
      defaultLocale: 'en' as const,
      supportedLocales: ['en', 'zh'] as SupportedLocale[],
      languageSwitching: true
    },
    features: {
      seo: false, // Disabled for faster development builds
      relatedContent: false, // Disabled for simpler development
      validation: true,
      staticGeneration: true
    }
  },

  /**
   * Production configuration
   */
  production: {
    provider: {
      type: 'contentlayer2' as const,
      options: {}
    },
    contentTypes: ['blog', 'product', 'case-study'] as ContentType[],
    i18n: {
      defaultLocale: 'en' as const,
      supportedLocales: ['en', 'zh'] as SupportedLocale[],
      languageSwitching: true
    },
    features: {
      seo: true,
      relatedContent: true,
      validation: true,
      staticGeneration: true
    }
  },

  /**
   * Testing configuration
   */
  testing: {
    provider: {
      type: 'contentlayer2' as const,
      options: {}
    },
    contentTypes: ['blog'] as ContentType[], // Minimal for faster tests
    i18n: {
      defaultLocale: 'en' as const,
      supportedLocales: ['en'] as SupportedLocale[], // Single locale for simpler tests
      languageSwitching: false
    },
    features: {
      seo: false,
      relatedContent: false,
      validation: false, // Disabled for faster tests
      staticGeneration: false
    }
  }
} as const
