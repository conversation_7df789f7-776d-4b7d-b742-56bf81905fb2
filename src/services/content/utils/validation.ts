/**
 * Content Validation Utilities
 * 
 * This module provides utilities for validating content items, slugs,
 * and other content-related data. It ensures data integrity and
 * consistency across the content management system.
 * 
 * Key Features:
 * - Content item structure validation
 * - Slug format validation
 * - Locale validation
 * - SEO metadata validation
 * - Extensible validation rules
 */

import type { 
  ContentItem, 
  ContentType, 
  SupportedLocale,
  ContentValidationResult,
  SEOMetadata
} from '../types'

/**
 * Validation configuration
 */
const VALIDATION_CONFIG = {
  slug: {
    minLength: 1,
    maxLength: 100,
    pattern: /^[a-z0-9]+(?:-[a-z0-9]+)*$/
  },
  title: {
    minLength: 1,
    maxLength: 200
  },
  description: {
    minLength: 10,
    maxLength: 500
  },
  tags: {
    maxCount: 10,
    maxLength: 50
  }
} as const

/**
 * Validate content slug format
 * 
 * Checks if a slug follows the expected format for content URLs.
 * Slugs should be lowercase, contain only letters, numbers, and hyphens,
 * and should not start or end with hyphens.
 * 
 * @param slug - Content slug to validate
 * @returns True if the slug is valid
 */
export function validateContentSlug(slug: string): boolean {
  if (!slug || typeof slug !== 'string') {
    return false
  }
  
  const { minLength, maxLength, pattern } = VALIDATION_CONFIG.slug
  
  return (
    slug.length >= minLength &&
    slug.length <= maxLength &&
    pattern.test(slug)
  )
}

/**
 * Validate content type
 * 
 * Checks if a string is a valid content type.
 * 
 * @param contentType - Content type to validate
 * @returns True if the content type is valid
 */
export function validateContentType(contentType: string): contentType is ContentType {
  return ['blog', 'product', 'case-study'].includes(contentType)
}

/**
 * Validate supported locale
 * 
 * Checks if a string is a supported locale.
 * 
 * @param locale - Locale to validate
 * @returns True if the locale is supported
 */
export function validateLocale(locale: string): locale is SupportedLocale {
  return ['en', 'zh'].includes(locale)
}

/**
 * Validate content item structure
 * 
 * Performs comprehensive validation of a content item,
 * checking all required fields and their formats.
 * 
 * @param content - Content item to validate
 * @returns Validation result with errors and warnings
 */
export function validateContentItem(content: any): ContentValidationResult {
  const errors: ContentValidationResult['errors'] = []
  const warnings: ContentValidationResult['warnings'] = []
  
  // Check required fields
  if (!content) {
    errors.push({
      field: 'content',
      message: 'Content item is required',
      severity: 'error'
    })
    return { valid: false, errors, warnings }
  }
  
  // Validate slug
  if (!content.slug) {
    errors.push({
      field: 'slug',
      message: 'Slug is required',
      severity: 'error'
    })
  } else if (!validateContentSlug(content.slug)) {
    errors.push({
      field: 'slug',
      message: 'Slug format is invalid. Use lowercase letters, numbers, and hyphens only.',
      severity: 'error'
    })
  }
  
  // Validate title
  if (!content.title) {
    errors.push({
      field: 'title',
      message: 'Title is required',
      severity: 'error'
    })
  } else {
    const { minLength, maxLength } = VALIDATION_CONFIG.title
    if (content.title.length < minLength) {
      errors.push({
        field: 'title',
        message: `Title must be at least ${minLength} characters long`,
        severity: 'error'
      })
    } else if (content.title.length > maxLength) {
      warnings.push({
        field: 'title',
        message: `Title is longer than recommended ${maxLength} characters`,
        suggestion: 'Consider shortening the title for better SEO'
      })
    }
  }
  
  // Validate description
  if (!content.description) {
    warnings.push({
      field: 'description',
      message: 'Description is recommended for better SEO',
      suggestion: 'Add a description to improve search engine visibility'
    })
  } else {
    const { minLength, maxLength } = VALIDATION_CONFIG.description
    if (content.description.length < minLength) {
      warnings.push({
        field: 'description',
        message: `Description should be at least ${minLength} characters for better SEO`,
        suggestion: 'Expand the description with more details'
      })
    } else if (content.description.length > maxLength) {
      warnings.push({
        field: 'description',
        message: `Description is longer than recommended ${maxLength} characters`,
        suggestion: 'Consider shortening the description'
      })
    }
  }
  
  // Validate content type
  if (!content.type) {
    errors.push({
      field: 'type',
      message: 'Content type is required',
      severity: 'error'
    })
  } else if (!validateContentType(content.type)) {
    errors.push({
      field: 'type',
      message: 'Invalid content type',
      severity: 'error'
    })
  }
  
  // Validate locale
  if (!content.locale) {
    errors.push({
      field: 'locale',
      message: 'Locale is required',
      severity: 'error'
    })
  } else if (!validateLocale(content.locale)) {
    errors.push({
      field: 'locale',
      message: 'Unsupported locale',
      severity: 'error'
    })
  }
  
  // Validate date
  if (!content.date) {
    errors.push({
      field: 'date',
      message: 'Date is required',
      severity: 'error'
    })
  } else if (isNaN(Date.parse(content.date))) {
    errors.push({
      field: 'date',
      message: 'Invalid date format',
      severity: 'error'
    })
  }
  
  // Validate published status
  if (typeof content.published !== 'boolean') {
    warnings.push({
      field: 'published',
      message: 'Published status should be explicitly set',
      suggestion: 'Set published to true or false'
    })
  }
  
  // Validate tags
  if (content.tags) {
    if (!Array.isArray(content.tags)) {
      errors.push({
        field: 'tags',
        message: 'Tags must be an array',
        severity: 'error'
      })
    } else {
      const { maxCount, maxLength } = VALIDATION_CONFIG.tags
      
      if (content.tags.length > maxCount) {
        warnings.push({
          field: 'tags',
          message: `Too many tags (${content.tags.length}). Recommended maximum is ${maxCount}`,
          suggestion: 'Remove less relevant tags'
        })
      }
      
      content.tags.forEach((tag: any, index: number) => {
        if (typeof tag !== 'string') {
          errors.push({
            field: `tags[${index}]`,
            message: 'Tag must be a string',
            severity: 'error'
          })
        } else if (tag.length > maxLength) {
          warnings.push({
            field: `tags[${index}]`,
            message: `Tag "${tag}" is too long (${tag.length} characters)`,
            suggestion: `Keep tags under ${maxLength} characters`
          })
        }
      })
    }
  }
  
  // Validate SEO metadata if present
  if (content.seo) {
    const seoValidation = validateSEOMetadata(content.seo)
    errors.push(...seoValidation.errors)
    warnings.push(...seoValidation.warnings)
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Validate SEO metadata
 * 
 * Validates SEO-specific metadata for content items.
 * 
 * @param seo - SEO metadata to validate
 * @returns Validation result
 */
export function validateSEOMetadata(seo: any): ContentValidationResult {
  const errors: ContentValidationResult['errors'] = []
  const warnings: ContentValidationResult['warnings'] = []
  
  if (!seo || typeof seo !== 'object') {
    return { valid: true, errors, warnings }
  }
  
  // Validate SEO title
  if (seo.title && typeof seo.title === 'string') {
    if (seo.title.length > 60) {
      warnings.push({
        field: 'seo.title',
        message: 'SEO title is longer than recommended 60 characters',
        suggestion: 'Shorten the title for better search engine display'
      })
    }
  }
  
  // Validate SEO description
  if (seo.description && typeof seo.description === 'string') {
    if (seo.description.length > 160) {
      warnings.push({
        field: 'seo.description',
        message: 'SEO description is longer than recommended 160 characters',
        suggestion: 'Shorten the description for better search engine display'
      })
    }
  }
  
  // Validate keywords
  if (seo.keywords) {
    if (!Array.isArray(seo.keywords)) {
      errors.push({
        field: 'seo.keywords',
        message: 'SEO keywords must be an array',
        severity: 'error'
      })
    } else if (seo.keywords.length > 10) {
      warnings.push({
        field: 'seo.keywords',
        message: 'Too many SEO keywords. Recommended maximum is 10',
        suggestion: 'Focus on the most relevant keywords'
      })
    }
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Sanitize content slug
 * 
 * Converts a string into a valid content slug format.
 * 
 * @param input - Input string to convert
 * @returns Sanitized slug
 */
export function sanitizeSlug(input: string): string {
  if (!input || typeof input !== 'string') {
    return ''
  }
  
  return input
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9\s-]/g, '') // Remove invalid characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
    .substring(0, VALIDATION_CONFIG.slug.maxLength) // Limit length
}

/**
 * Check if content item is complete
 * 
 * Determines if a content item has all the necessary fields
 * for publication and good SEO.
 * 
 * @param content - Content item to check
 * @returns Completeness information
 */
export function checkContentCompleteness(content: ContentItem): {
  complete: boolean
  score: number
  missing: string[]
  recommendations: string[]
} {
  const missing: string[] = []
  const recommendations: string[] = []
  let score = 0
  const maxScore = 10
  
  // Required fields (6 points)
  if (content.title) score += 1
  else missing.push('title')
  
  if (content.description) score += 1
  else missing.push('description')
  
  if (content.slug && validateContentSlug(content.slug)) score += 1
  else missing.push('valid slug')
  
  if (content.date) score += 1
  else missing.push('date')
  
  if (content.type) score += 1
  else missing.push('content type')
  
  if (content.locale) score += 1
  else missing.push('locale')
  
  // Optional but recommended fields (4 points)
  if (content.image) {
    score += 1
  } else {
    recommendations.push('Add a featured image')
  }
  
  if (content.tags && content.tags.length > 0) {
    score += 1
  } else {
    recommendations.push('Add relevant tags')
  }
  
  if (content.seo?.title) {
    score += 1
  } else {
    recommendations.push('Add SEO title')
  }
  
  if (content.seo?.description) {
    score += 1
  } else {
    recommendations.push('Add SEO description')
  }
  
  return {
    complete: missing.length === 0,
    score: Math.round((score / maxScore) * 100),
    missing,
    recommendations
  }
}
