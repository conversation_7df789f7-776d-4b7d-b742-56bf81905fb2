/**
 * Content Detection Utilities
 * 
 * This module provides utilities for URL analysis and content type detection.
 * It parses pathnames to determine what type of content is being viewed and
 * extracts relevant information like content type and slug.
 * 
 * Key Features:
 * - URL pattern matching for different content types
 * - Locale-aware path parsing
 * - Support for both content detail pages and list pages
 * - Extensible pattern matching system
 * 
 * Architecture:
 * This utility layer is designed to be framework-agnostic and can work with
 * any URL structure. It provides the foundation for content routing and
 * language switching functionality.
 */

import type { ContentType, ContentPageInfo } from '../types'

/**
 * Content URL patterns configuration
 * 
 * Centralizes the URL patterns for different content types.
 * This makes it easy to modify URL structures without affecting
 * the detection logic throughout the application.
 */
const CONTENT_PATTERNS = {
  blog: '/blogs/',
  product: '/products/',
  'case-study': '/case-studies/'
} as const

/**
 * Detect content page information from URL pathname
 * 
 * Analyzes a URL pathname to determine if it represents a content page
 * and extracts the content type and slug information. This function
 * handles both localized and non-localized URLs consistently.
 * 
 * URL Pattern Examples:
 * - '/blogs/my-post' -> { contentType: 'blog', slug: 'my-post', isContentPage: true }
 * - '/zh/products/my-product' -> { contentType: 'product', slug: 'my-product', isContentPage: true }
 * - '/case-studies/' -> { contentType: 'case-study', slug: '', isContentPage: false }
 * - '/about' -> { contentType: 'blog', slug: '', isContentPage: false }
 * 
 * @param pathname - Current pathname from router or URL
 * @param locale - Current locale identifier
 * @returns Content page information object
 */
export function detectContentPage(pathname: string, locale: string): ContentPageInfo {
  // Normalize pathname by removing locale prefix
  const cleanPath = normalizePathname(pathname, locale)
  
  // Check each content pattern
  for (const [contentType, pattern] of Object.entries(CONTENT_PATTERNS)) {
    if (cleanPath.startsWith(pattern)) {
      const slug = extractSlugFromPath(cleanPath, pattern)
      
      return {
        contentType: contentType as ContentType,
        slug,
        isContentPage: slug.length > 0,
        pathSegments: cleanPath.split('/').filter(Boolean)
      }
    }
  }

  // Default case for non-content pages
  return {
    contentType: 'blog', // default fallback
    slug: '',
    isContentPage: false,
    pathSegments: cleanPath.split('/').filter(Boolean)
  }
}

/**
 * Get base path for content type
 * 
 * Maps content types to their corresponding URL base paths.
 * This centralizes URL structure configuration for consistency.
 * 
 * @param contentType - Type of content
 * @returns Base path string for the content type
 */
export function getContentBasePath(contentType: ContentType): string {
  return CONTENT_PATTERNS[contentType] || ''
}

/**
 * Check if a pathname represents a content list page
 * 
 * Determines if the given pathname is a content list page
 * (e.g., /blogs/, /products/) rather than a specific content item.
 * 
 * @param pathname - URL pathname to check
 * @param locale - Current locale
 * @returns True if this is a content list page
 */
export function isContentListPage(pathname: string, locale: string): boolean {
  const cleanPath = normalizePathname(pathname, locale)
  
  return Object.values(CONTENT_PATTERNS).some(pattern => 
    cleanPath === pattern.slice(0, -1) || cleanPath === pattern
  )
}

/**
 * Check if a pathname represents a content detail page
 * 
 * Determines if the given pathname is a content detail page
 * (e.g., /blogs/my-post, /products/my-product) with a specific slug.
 * 
 * @param pathname - URL pathname to check
 * @param locale - Current locale
 * @returns True if this is a content detail page
 */
export function isContentDetailPage(pathname: string, locale: string): boolean {
  const pageInfo = detectContentPage(pathname, locale)
  return pageInfo.isContentPage && pageInfo.slug !== null && pageInfo.slug.length > 0
}

/**
 * Extract content type from pathname
 * 
 * Extracts just the content type from a pathname without
 * additional information like slug or locale.
 * 
 * @param pathname - URL pathname
 * @param locale - Current locale
 * @returns Content type or null if not a content page
 */
export function extractContentType(pathname: string, locale: string): ContentType | null {
  const pageInfo = detectContentPage(pathname, locale)
  return pageInfo.isContentPage ? pageInfo.contentType : null
}

/**
 * Validate content slug format
 * 
 * Checks if a slug follows the expected format for content URLs.
 * This helps ensure URL consistency and prevents invalid slugs.
 * 
 * @param slug - Content slug to validate
 * @returns True if the slug is valid
 */
export function validateContentSlug(slug: string): boolean {
  if (!slug || typeof slug !== 'string') {
    return false
  }
  
  // Slug should be lowercase, contain only letters, numbers, and hyphens
  // Should not start or end with hyphen, and should not contain consecutive hyphens
  const slugPattern = /^[a-z0-9]+(?:-[a-z0-9]+)*$/
  return slugPattern.test(slug) && slug.length >= 1 && slug.length <= 100
}

// ==========================================
// Private Helper Functions
// ==========================================

/**
 * Normalize pathname by removing locale prefix
 * 
 * @private
 * @param pathname - Original pathname
 * @param locale - Current locale
 * @returns Normalized pathname without locale prefix
 */
function normalizePathname(pathname: string, locale: string): string {
  // Remove locale prefix if present (except for default locale 'en')
  if (locale !== 'en' && pathname.startsWith(`/${locale}`)) {
    return pathname.replace(`/${locale}`, '') || '/'
  }
  
  return pathname
}

/**
 * Extract slug from path using pattern
 * 
 * @private
 * @param path - Normalized path
 * @param pattern - Content pattern to match
 * @returns Extracted slug
 */
function extractSlugFromPath(path: string, pattern: string): string {
  const slug = path.replace(pattern, '').split('/')[0]
  return slug || ''
}

/**
 * Content detection configuration
 * 
 * Allows for runtime configuration of content detection behavior.
 * This can be useful for testing or when URL patterns need to be
 * modified based on environment or configuration.
 */
export interface ContentDetectionConfig {
  /** Custom content patterns */
  patterns?: Partial<typeof CONTENT_PATTERNS>
  /** Whether to enable strict slug validation */
  strictValidation?: boolean
  /** Custom slug validation pattern */
  slugPattern?: RegExp
}

/**
 * Configure content detection behavior
 * 
 * Allows customization of content detection patterns and validation rules.
 * This is useful for testing or when different URL structures are needed.
 * 
 * @param config - Configuration options
 */
export function configureContentDetection(config: ContentDetectionConfig): void {
  // This could be implemented to allow runtime configuration
  // For now, we keep the patterns static for performance
  console.warn('Content detection configuration is not yet implemented')
}
