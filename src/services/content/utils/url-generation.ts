/**
 * URL Generation Utilities
 * 
 * This module provides utilities for generating URLs for content pages across
 * different languages and content types. It ensures consistent URL structure
 * throughout the application and provides a centralized place for URL logic.
 * 
 * Key Features:
 * - Consistent URL structure across all content types
 * - Locale-aware URL generation with configurable patterns
 * - Support for both content detail pages and list pages
 * - SEO-friendly canonical and alternate URL generation
 * - Extensible URL pattern system
 * 
 * URL Structure:
 * - English (default): '/blogs/my-post', '/products/my-product'
 * - Other languages: '/zh/blogs/my-post', '/zh/products/my-product'
 * - List pages: '/blogs', '/zh/blogs'
 */

import type { ContentType, SupportedLocale } from '../types'

/**
 * URL generation configuration
 * 
 * Centralizes URL structure configuration for easy maintenance
 * and consistent URL generation across the application.
 */
const URL_CONFIG = {
  /** Content type to URL path mapping */
  contentPaths: {
    blog: 'blogs',
    product: 'products',
    'case-study': 'case-studies'
  } as const,
  
  /** Default locale (no prefix in URL) */
  defaultLocale: 'en' as const,
  
  /** Whether to include trailing slashes */
  trailingSlash: false,
  
  /** Base URL for absolute URLs */
  baseUrl: process.env.NEXT_PUBLIC_BASE_URL || ''
} as const

/**
 * Generate URL for content in a specific locale
 * 
 * Constructs the correct URL for accessing content in a specific language,
 * following the application's URL structure conventions. Handles both
 * individual content pages and list pages.
 * 
 * URL Examples:
 * - generateContentUrl('blog', 'my-post', 'en') -> '/blogs/my-post'
 * - generateContentUrl('product', 'my-product', 'zh') -> '/zh/products/my-product'
 * - generateContentUrl('blog', '', 'en') -> '/blogs'
 * 
 * @param contentType - Type of content
 * @param slug - Content identifier (empty for list pages)
 * @param locale - Target locale code
 * @param options - Additional URL generation options
 * @returns Properly formatted URL string
 */
export function generateContentUrl(
  contentType: ContentType,
  slug: string,
  locale: SupportedLocale,
  options: {
    /** Whether to generate absolute URL */
    absolute?: boolean
    /** Custom base URL */
    baseUrl?: string
    /** Additional query parameters */
    params?: Record<string, string>
  } = {}
): string {
  const { absolute = false, baseUrl = URL_CONFIG.baseUrl, params } = options
  
  // Get the path segment for this content type
  const contentPath = URL_CONFIG.contentPaths[contentType]
  
  // Build locale prefix (empty for default locale)
  const localePrefix = locale === URL_CONFIG.defaultLocale ? '' : `/${locale}`
  
  // Build the path components
  const pathComponents = [localePrefix, `/${contentPath}`]
  
  // Add slug if provided
  if (slug && slug.trim()) {
    pathComponents.push(`/${slug}`)
  }
  
  // Combine path components
  let path = pathComponents.join('')
  
  // Add trailing slash if configured
  if (URL_CONFIG.trailingSlash && !path.endsWith('/')) {
    path += '/'
  }
  
  // Add query parameters if provided
  if (params && Object.keys(params).length > 0) {
    const searchParams = new URLSearchParams(params)
    path += `?${searchParams.toString()}`
  }
  
  // Return absolute or relative URL
  return absolute ? `${baseUrl}${path}` : path
}

/**
 * Generate canonical URL for content
 * 
 * Generates the canonical URL for SEO purposes. The canonical URL
 * typically uses the default locale unless the content only exists
 * in other languages.
 * 
 * @param contentType - Type of content
 * @param slug - Content identifier
 * @param availableLocales - Locales where content exists
 * @param options - URL generation options
 * @returns Canonical URL string
 */
export function generateCanonicalUrl(
  contentType: ContentType,
  slug: string,
  availableLocales: SupportedLocale[],
  options: { baseUrl?: string } = {}
): string {
  // Prefer default locale if available
  const canonicalLocale = availableLocales.includes(URL_CONFIG.defaultLocale)
    ? URL_CONFIG.defaultLocale
    : availableLocales[0] || URL_CONFIG.defaultLocale
  
  return generateContentUrl(contentType, slug, canonicalLocale, {
    absolute: true,
    baseUrl: options.baseUrl
  })
}

/**
 * Generate alternate URLs for different languages
 * 
 * Creates a map of alternate URLs for the same content in different
 * languages. Useful for generating hreflang attributes for SEO.
 * 
 * @param contentType - Type of content
 * @param slug - Content identifier
 * @param availableLocales - Locales where content exists
 * @param options - URL generation options
 * @returns Map of locale to URL
 */
export function generateAlternateUrls(
  contentType: ContentType,
  slug: string,
  availableLocales: SupportedLocale[],
  options: { baseUrl?: string } = {}
): Record<SupportedLocale, string> {
  const alternateUrls = {} as Record<SupportedLocale, string>
  
  for (const locale of availableLocales) {
    alternateUrls[locale] = generateContentUrl(contentType, slug, locale, {
      absolute: true,
      baseUrl: options.baseUrl
    })
  }
  
  return alternateUrls
}

/**
 * Generate content list page URL
 * 
 * Generates URL for content list pages (e.g., /blogs, /products).
 * This is a convenience function for generating list page URLs.
 * 
 * @param contentType - Type of content
 * @param locale - Target locale
 * @param options - URL generation options
 * @returns Content list page URL
 */
export function generateContentListUrl(
  contentType: ContentType,
  locale: SupportedLocale,
  options: {
    absolute?: boolean
    baseUrl?: string
    page?: number
    category?: string
  } = {}
): string {
  const { page, category, ...urlOptions } = options
  
  // Build query parameters
  const params: Record<string, string> = {}
  if (page && page > 1) {
    params.page = page.toString()
  }
  if (category) {
    params.category = category
  }
  
  return generateContentUrl(contentType, '', locale, {
    ...urlOptions,
    params: Object.keys(params).length > 0 ? params : undefined
  })
}

/**
 * Parse content URL to extract components
 * 
 * Parses a content URL to extract its components like content type,
 * slug, and locale. Useful for reverse URL operations.
 * 
 * @param url - URL to parse
 * @returns Parsed URL components
 */
export function parseContentUrl(url: string): {
  contentType: ContentType | null
  slug: string | null
  locale: SupportedLocale
  isListPage: boolean
} {
  // Remove base URL if present
  const path = url.replace(URL_CONFIG.baseUrl, '')
  
  // Remove query parameters
  const [pathname] = path.split('?')
  
  // Split path into segments
  const segments = pathname.split('/').filter(Boolean)
  
  // Check if first segment is a locale
  let locale: SupportedLocale = URL_CONFIG.defaultLocale
  let pathStart = 0
  
  if (segments[0] && ['en', 'zh'].includes(segments[0])) {
    locale = segments[0] as SupportedLocale
    pathStart = 1
  }
  
  // Check if next segment is a content type
  const contentSegment = segments[pathStart]
  let contentType: ContentType | null = null
  
  for (const [type, path] of Object.entries(URL_CONFIG.contentPaths)) {
    if (contentSegment === path) {
      contentType = type as ContentType
      break
    }
  }
  
  // Extract slug if present
  const slug = segments[pathStart + 1] || null
  const isListPage = contentType !== null && slug === null
  
  return {
    contentType,
    slug,
    locale,
    isListPage
  }
}

/**
 * Validate URL format
 * 
 * Validates if a URL follows the expected format for content URLs.
 * Useful for URL validation and error checking.
 * 
 * @param url - URL to validate
 * @returns Validation result
 */
export function validateContentUrl(url: string): {
  valid: boolean
  errors: string[]
} {
  const errors: string[] = []
  
  try {
    const parsed = parseContentUrl(url)
    
    // Check if content type is valid
    if (parsed.contentType && !Object.keys(URL_CONFIG.contentPaths).includes(parsed.contentType)) {
      errors.push(`Invalid content type: ${parsed.contentType}`)
    }
    
    // Check if locale is supported
    if (!['en', 'zh'].includes(parsed.locale)) {
      errors.push(`Unsupported locale: ${parsed.locale}`)
    }
    
    // Check slug format if present
    if (parsed.slug && !/^[a-z0-9-]+$/.test(parsed.slug)) {
      errors.push(`Invalid slug format: ${parsed.slug}`)
    }
    
  } catch (error) {
    errors.push(`URL parsing error: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * Get content base path for a content type
 * 
 * Returns the base path segment for a given content type.
 * This is useful for URL construction and pattern matching.
 * 
 * @param contentType - Content type
 * @returns Base path segment
 */
export function getContentBasePath(contentType: ContentType): string {
  return URL_CONFIG.contentPaths[contentType] || ''
}

/**
 * Configure URL generation behavior
 * 
 * Allows runtime configuration of URL generation settings.
 * Useful for testing or environment-specific configurations.
 * 
 * @param config - Configuration options
 */
export function configureUrlGeneration(config: {
  baseUrl?: string
  trailingSlash?: boolean
  defaultLocale?: SupportedLocale
}): void {
  // This could be implemented to allow runtime configuration
  // For now, we keep the configuration static for performance
  console.warn('URL generation configuration is not yet implemented')
}
