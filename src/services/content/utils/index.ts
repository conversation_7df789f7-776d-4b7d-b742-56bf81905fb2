/**
 * Content Utilities - Unified Export
 * 
 * This module provides a unified interface to all content utility functions.
 * These utilities are designed to be lightweight, framework-agnostic helpers
 * that can be used throughout the application for content-related operations.
 * 
 * Utility Categories:
 * - Content Detection: URL parsing and content type detection
 * - URL Generation: Consistent URL generation for content pages
 * - Validation: Content validation and sanitization utilities
 * 
 * Usage:
 * ```typescript
 * import {
 *   detectContentPage,
 *   generateContentUrl,
 *   validateContentSlug
 * } from '@/services/content/utils'
 * ```
 * 
 * Architecture:
 * These utilities form the presentation layer of the content service architecture.
 * They are designed to be pure functions with no side effects, making them
 * easy to test and reason about.
 */

// Content detection utilities
export {
  detectContentPage,
  getContentBasePath,
  isContentListPage,
  isContentDetailPage,
  extractContentType,
  validateContentSlug as validateSlugFromDetection,
  configureContentDetection
} from './content-detection'

// URL generation utilities
export {
  generateContentUrl,
  generateCanonicalUrl,
  generateAlternateUrls,
  generateContentListUrl,
  parseContentUrl,
  validateContentUrl,
  getContentBasePath as getContentBasePathFromUrl,
  configureUrlGeneration
} from './url-generation'

// Validation utilities
export {
  validateContentSlug,
  validateContentType,
  validateLocale,
  validateContentItem,
  validateSEOMetadata,
  sanitizeSlug,
  checkContentCompleteness
} from './validation'

// Re-export types for convenience
export type {
  ContentType,
  ContentPageInfo,
  SupportedLocale,
  ContentValidationResult
} from '../types'

// Import types for internal use
import type { SupportedLocale, ContentType } from '../types'

/**
 * Content utility configuration interface
 *
 * Allows configuration of utility behavior across all utility modules.
 * This provides a centralized way to customize utility functions.
 */
export interface ContentUtilsConfig {
  /** URL generation configuration */
  urls?: {
    baseUrl?: string
    trailingSlash?: boolean
    defaultLocale?: SupportedLocale
  }
  
  /** Validation configuration */
  validation?: {
    strictMode?: boolean
    customRules?: Record<string, any>
  }
  
  /** Content detection configuration */
  detection?: {
    patterns?: Partial<Record<string, string>>
    strictValidation?: boolean
    customSlugPattern?: string
  }
}

// Import configuration functions for internal use
import { configureUrlGeneration, parseContentUrl, validateContentUrl, generateContentUrl } from './url-generation'
import { configureContentDetection, detectContentPage, isContentListPage, isContentDetailPage } from './content-detection'
import { validateContentSlug, sanitizeSlug, validateContentType, validateLocale } from './validation'

/**
 * Configure content utilities
 *
 * Provides a centralized way to configure all content utility functions.
 * This is useful for testing or when different configurations are needed
 * for different environments.
 *
 * @param config - Configuration options
 */
export function configureContentUtils(config: ContentUtilsConfig): void {
  // Apply URL generation configuration
  if (config.urls) {
    configureUrlGeneration(config.urls)
  }

  // Apply content detection configuration
  if (config.detection) {
    configureContentDetection(config.detection)
  }

  // Note: Validation configuration could be implemented here
  // when more advanced validation customization is needed
}

/**
 * Content utility helpers
 * 
 * Provides commonly used combinations of utility functions
 * for convenience and consistency.
 */
export const ContentUtils = {
  /**
   * Parse and validate a content URL
   * 
   * Combines URL parsing with validation for a complete URL analysis.
   * 
   * @param url - URL to parse and validate
   * @returns Parsed and validated URL information
   */
  parseAndValidateUrl(url: string) {
    const parsed = parseContentUrl(url)
    const validation = validateContentUrl(url)
    
    return {
      ...parsed,
      validation
    }
  },
  
  /**
   * Generate and validate a content URL
   * 
   * Generates a URL and validates it to ensure correctness.
   * 
   * @param contentType - Content type
   * @param slug - Content slug
   * @param locale - Target locale
   * @returns Generated URL with validation result
   */
  generateAndValidateUrl(
    contentType: ContentType,
    slug: string,
    locale: SupportedLocale
  ) {
    const url = generateContentUrl(contentType, slug, locale)
    const validation = validateContentUrl(url)
    
    return {
      url,
      validation
    }
  },
  
  /**
   * Detect and validate content page
   * 
   * Combines content page detection with validation for complete analysis.
   * 
   * @param pathname - URL pathname
   * @param locale - Current locale
   * @returns Content page information with validation
   */
  detectAndValidateContentPage(pathname: string, locale: string) {
    const pageInfo = detectContentPage(pathname, locale)
    
    let validation: { valid: boolean; errors: string[] } = { valid: true, errors: [] }
    if (pageInfo.isContentPage && pageInfo.slug) {
      validation = {
        valid: validateContentSlug(pageInfo.slug),
        errors: validateContentSlug(pageInfo.slug)
          ? []
          : ['Invalid slug format']
      }
    }
    
    return {
      ...pageInfo,
      validation
    }
  },
  
  /**
   * Sanitize and validate slug
   * 
   * Sanitizes a slug and validates the result.
   * 
   * @param input - Input string to convert to slug
   * @returns Sanitized slug with validation result
   */
  sanitizeAndValidateSlug(input: string) {
    const sanitized = sanitizeSlug(input)
    const valid = validateContentSlug(sanitized)
    
    return {
      slug: sanitized,
      valid,
      original: input
    }
  }
} as const

/**
 * Type guard utilities
 * 
 * Provides type guards for common content-related type checking.
 */
export const TypeGuards = {
  /**
   * Check if a value is a valid content type
   */
  isContentType: validateContentType,
  
  /**
   * Check if a value is a supported locale
   */
  isLocale: validateLocale,
  
  /**
   * Check if a value is a valid content slug
   */
  isValidSlug: validateContentSlug,
  
  /**
   * Check if a pathname represents a content page
   */
  isContentPage(pathname: string, locale: string): boolean {
    const pageInfo = detectContentPage(pathname, locale)
    return pageInfo.isContentPage
  },
  
  /**
   * Check if a pathname represents a content list page
   */
  isListPage: isContentListPage,
  
  /**
   * Check if a pathname represents a content detail page
   */
  isDetailPage: isContentDetailPage
} as const
