/**
 * CMS Services Module
 *
 * This module provides a unified interface for all CMS service-related
 * functionality including service implementations, factory functions,
 * and configuration utilities. It follows the index.ts design principle
 * by only containing re-exports and no implementation code.
 *
 * All concrete implementations are located in separate, focused modules
 * for better maintainability and organization.
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */

// Export CMS service implementation
export { CMSService } from './cms-service'

// Export factory functions and singleton instances
export {
  createCMSService,
  createValidatedCMSService,
  createTestCMSService,
  initializeCMS,
  getCMSService,
  resetCMSService,
  cms
} from './cms-factory'

// Export configuration utilities
export {
  getDefaultCMSConfig,
  getDevelopmentCMSConfig,
  getProductionCMSConfig,
  getTestingCMSConfig,
  getCMSConfigByEnvironment,
  createCustomCMSConfig,
  validateCMSConfig
} from './cms-config'

// Cache services removed for Cloudflare Workers optimization
// Previously exported cache services are no longer needed

// Re-export types for convenience
export type {
  ContentProvider,
  ContentItem,
  ContentType,
  QueryOptions,
  ContentMetadata,
  LanguageVersion,
  CMSConfig
} from '../types'
