/**
 * CMS Service Configuration
 * 
 * This module provides default CMS service configuration settings and
 * configuration presets for different deployment scenarios.
 * The configurations are optimized for typical CMS usage patterns
 * and provide sensible defaults for various CMS providers.
 * 
 * The configuration system supports multiple CMS backends
 * and provides fine-grained control over feature flags,
 * content types, and localization settings.
 * 
 * Key Features:
 * - Default configuration optimized for CMS workloads
 * - Environment-specific configuration presets
 * - Comprehensive feature flag settings
 * - Multi-language content support configuration
 * - Provider-specific optimization options
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */

import type { CMSConfig } from '../types'

/**
 * Default CMS Configuration
 * 
 * Provides a comprehensive default configuration for the CMS service
 * that is optimized for typical CMS usage patterns. This configuration
 * uses Contentlayer2 as the default provider with reasonable feature
 * flags and multilingual support.
 * 
 * The default configuration is designed to:
 * - Provide good performance for most CMS workloads
 * - Support common content types (blog, product, case-study)
 * - Enable essential features like caching and SEO
 * - Support English and Chinese locales by default
 * - Work well in both development and production environments
 * 
 * @example
 * ```typescript
 * import { getDefaultCMSConfig } from './cms-config'
 * 
 * const config = getDefaultCMSConfig()
 * await cmsService.initialize(config)
 * ```
 */
export function getDefaultCMSConfig(): CMSConfig {
  return {
    // Provider configuration
    provider: 'contentlayer2',
    
    // Content configuration
    contentTypes: ['blog', 'product', 'case-study'],
    defaultLocale: 'en',
    supportedLocales: ['en', 'zh'],
    
    // Feature flags - enable commonly used features
    features: {
      seo: true,             // Enable SEO data generation
      relatedContent: true,  // Enable related content suggestions
      languageSwitching: true // Enable language switching functionality
    }
  }
}

/**
 * Development Environment Configuration
 * 
 * Optimized configuration for development environments with
 * debugging features enabled and development-friendly settings.
 * This configuration prioritizes developer experience over performance.
 * 
 * @example
 * ```typescript
 * import { getDevelopmentCMSConfig } from './cms-config'
 * 
 * const devConfig = getDevelopmentCMSConfig()
 * await cmsService.initialize(devConfig)
 * ```
 */
export function getDevelopmentCMSConfig(): CMSConfig {
  const baseConfig = getDefaultCMSConfig()
  
  return {
    ...baseConfig,
    features: {
      ...baseConfig.features,
      seo: false,            // Disable SEO in development for faster builds
      relatedContent: false, // Disable to reduce complexity during development
      languageSwitching: true // Keep for testing multilingual features
    },
    // Development-specific settings can be added here
    development: {
      enableDebugLogs: true,
      hotReload: true,
      mockData: false,
      validateContent: true
    }
  }
}

/**
 * Production Environment Configuration
 * 
 * Optimized configuration for production environments with
 * performance features enabled and production-ready settings.
 * This configuration prioritizes performance and SEO.
 * 
 * @example
 * ```typescript
 * import { getProductionCMSConfig } from './cms-config'
 * 
 * const prodConfig = getProductionCMSConfig()
 * await cmsService.initialize(prodConfig)
 * ```
 */
export function getProductionCMSConfig(): CMSConfig {
  const baseConfig = getDefaultCMSConfig()
  
  return {
    ...baseConfig,
    features: {
      ...baseConfig.features,
      seo: true,             // Enable SEO for search engine optimization
      relatedContent: true,  // Enable for better user engagement
      languageSwitching: true // Enable for international users
    },
    // Production-specific settings
    seo: {
      generateSitemap: true,
      generateRobotsTxt: true
    },
    performance: {
      preloadCriticalContent: true,
      lazyLoadImages: true,
      optimizeImages: true,
      maxConcurrentRequests: 20
    },
    development: {
      enableDebugLogs: false,
      hotReload: false,
      mockData: false,
      validateContent: false
    }
  }
}

/**
 * Testing Environment Configuration
 * 
 * Optimized configuration for testing environments with
 * minimal features enabled and testing-friendly settings.
 * This configuration prioritizes test reliability and speed.
 * 
 * @example
 * ```typescript
 * import { getTestingCMSConfig } from './cms-config'
 * 
 * const testConfig = getTestingCMSConfig()
 * await cmsService.initialize(testConfig)
 * ```
 */
export function getTestingCMSConfig(): CMSConfig {
  const baseConfig = getDefaultCMSConfig()
  
  return {
    ...baseConfig,
    features: {
      seo: false,            // Disable SEO for faster test execution
      relatedContent: false, // Disable to reduce test complexity
      languageSwitching: false // Disable for simpler test scenarios
    },
    // Testing-specific settings
    development: {
      enableDebugLogs: false,
      hotReload: false,
      mockData: true,        // Use mock data for consistent tests
      validateContent: true  // Validate content structure in tests
    }
  }
}

/**
 * Get CMS configuration by environment
 * 
 * Utility function to get the appropriate CMS configuration
 * based on the current environment. This provides a convenient
 * way to automatically select the right configuration without
 * manual environment checking.
 * 
 * @param environment - The environment name ('development', 'production', 'test')
 * @returns Appropriate CMS configuration for the environment
 * 
 * @example
 * ```typescript
 * const config = getCMSConfigByEnvironment(process.env.NODE_ENV)
 * await cmsService.initialize(config)
 * ```
 */
export function getCMSConfigByEnvironment(environment?: string): CMSConfig {
  switch (environment) {
    case 'development':
      return getDevelopmentCMSConfig()
    case 'production':
      return getProductionCMSConfig()
    case 'test':
    case 'testing':
      return getTestingCMSConfig()
    default:
      return getDefaultCMSConfig()
  }
}

/**
 * Create custom CMS configuration
 * 
 * Utility function to create a custom CMS configuration by
 * merging user-provided options with the default configuration.
 * This allows for easy customization while maintaining sensible defaults.
 * 
 * @param overrides - Configuration overrides to apply
 * @returns Custom CMS configuration
 * 
 * @example
 * ```typescript
 * const customConfig = createCustomCMSConfig({
 *   provider: 'strapi',
 *   contentTypes: ['blog', 'product'],
 *   features: { cache: false } // Override specific features
 * })
 * ```
 */
export function createCustomCMSConfig(overrides: Partial<CMSConfig>): CMSConfig {
  const defaultConfig = getDefaultCMSConfig()
  
  return {
    ...defaultConfig,
    ...overrides,
    features: {
      ...defaultConfig.features,
      ...(overrides.features || {})
    },
    // Merge optional configurations safely
    seo: overrides.seo ? {
      ...defaultConfig.seo,
      ...overrides.seo
    } : defaultConfig.seo,
    i18n: overrides.i18n ? {
      ...defaultConfig.i18n,
      ...overrides.i18n
    } : defaultConfig.i18n,
    performance: overrides.performance ? {
      ...defaultConfig.performance,
      ...overrides.performance
    } : defaultConfig.performance,
    development: overrides.development ? {
      ...defaultConfig.development,
      ...overrides.development
    } : defaultConfig.development
  }
}

/**
 * Validate CMS configuration
 * 
 * Utility function to validate a CMS configuration object
 * and ensure all required fields are present and valid.
 * This function provides detailed error messages for
 * configuration issues.
 * 
 * @param config - CMS configuration to validate
 * @throws {Error} When configuration is invalid
 * 
 * @example
 * ```typescript
 * try {
 *   validateCMSConfig(config)
 *   await cmsService.initialize(config)
 * } catch (error) {
 *   console.error('Invalid configuration:', error.message)
 * }
 * ```
 */
export function validateCMSConfig(config: CMSConfig): void {
  const errors: string[] = []

  // Validate required fields
  if (!config.provider) {
    errors.push('Provider is required')
  }

  if (!config.contentTypes || config.contentTypes.length === 0) {
    errors.push('At least one content type must be specified')
  }

  if (!config.defaultLocale) {
    errors.push('Default locale is required')
  }

  if (!config.supportedLocales || config.supportedLocales.length === 0) {
    errors.push('At least one supported locale must be specified')
  } else if (!config.supportedLocales.includes(config.defaultLocale)) {
    errors.push('Default locale must be included in supported locales')
  }

  if (!config.features) {
    errors.push('Features configuration is required')
  }

  // Validate provider
  const validProviders = ['contentlayer2', 'strapi', 'sanity', 'contentful', 'nextjs-mdx']
  if (config.provider && !validProviders.includes(config.provider)) {
    errors.push(`Invalid provider: ${config.provider}. Valid providers: ${validProviders.join(', ')}`)
  }

  // Validate content types
  const validContentTypes = ['blog', 'product', 'case-study']
  if (config.contentTypes) {
    const invalidTypes = config.contentTypes.filter(type => !validContentTypes.includes(type))
    if (invalidTypes.length > 0) {
      errors.push(`Invalid content types: ${invalidTypes.join(', ')}. Valid types: ${validContentTypes.join(', ')}`)
    }
  }

  if (errors.length > 0) {
    throw new Error(`CMS configuration validation failed:\n${errors.join('\n')}`)
  }
}
