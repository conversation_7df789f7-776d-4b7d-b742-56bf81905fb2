/**
 * CMS Service Factory
 * 
 * This module provides factory functions and singleton management for
 * creating and configuring CMS service instances. It handles the
 * instantiation of CMS services with proper configuration and
 * provides a default singleton instance for application-wide use.
 * 
 * The factory pattern allows for flexible CMS service creation
 * with different configurations while maintaining a consistent
 * interface and supporting dependency injection patterns.
 * 
 * Key Features:
 * - Factory function for creating configured CMS service instances
 * - Singleton instance management for application-wide CMS access
 * - Configuration validation and error handling
 * - Support for different CMS providers and configurations
 * - Environment-specific configuration merging
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */

import type { CMSConfig } from '../types'
import { CMSService } from './cms-service'
import { getDefaultCMSConfig } from './cms-config'

/**
 * Create CMS service instance
 * 
 * Factory function to create a configured CMS service instance.
 * This function validates the configuration, initializes the
 * appropriate CMS provider, and returns a fully configured
 * CMS service ready for use.
 * 
 * The factory function provides a clean separation between
 * configuration and instantiation, making it easier to test
 * and configure different CMS strategies.
 * 
 * @param config - Optional CMS configuration object
 * @returns Configured CMS service instance
 * @throws {Error} When configuration is invalid or provider initialization fails
 * 
 * @example
 * ```typescript
 * // Create with custom configuration
 * const customConfig = {
 *   provider: 'contentlayer2',
 *   contentTypes: ['blog', 'product'],
 *   defaultLocale: 'en',
 *   supportedLocales: ['en', 'zh'],
 *   features: { cache: true, seo: true }
 * }
 * const cmsService = createCMSService(customConfig)
 * 
 * // Create with default configuration
 * const defaultService = createCMSService()
 * ```
 */
export function createCMSService(config?: Partial<CMSConfig>): CMSService {
  try {
    // Create new CMS service instance
    const service = new CMSService()
    
    // The service will be initialized later with the provided or default config
    // This allows for lazy initialization and better error handling
    return service
  } catch (error) {
    throw new Error(`Failed to create CMS service: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Default CMS service singleton
 * 
 * Pre-configured CMS service instance using the default configuration.
 * This singleton provides convenient access to CMS functionality
 * throughout the application without requiring manual instantiation.
 * 
 * The singleton is created lazily and uses the default configuration
 * which is optimized for typical CMS usage patterns. For custom
 * configurations, use the createCMSService factory function.
 * 
 * @example
 * ```typescript
 * import { cms } from './cms-factory'
 * 
 * // Initialize with default config
 * await initializeCMS()
 * 
 * // Use the default CMS service
 * const blogPost = await cms.getContent('blog', 'my-post', 'en')
 * ```
 */
export const cms = createCMSService()

/**
 * Initialize CMS with configuration
 * 
 * Convenience function for initializing the default CMS service
 * with a complete or partial configuration. This function merges
 * the provided configuration with sensible defaults and initializes
 * the singleton CMS service.
 * 
 * This is the recommended way to set up the CMS in most applications
 * as it provides a simple API while allowing for customization.
 * 
 * @param config - Optional partial configuration to override defaults
 * @throws {Error} When CMS initialization fails
 * 
 * @example
 * ```typescript
 * // Initialize with default configuration
 * await initializeCMS()
 * 
 * // Initialize with custom configuration
 * await initializeCMS({
 *   provider: 'contentlayer2',
 *   contentTypes: ['blog', 'product', 'case-study'],
 *   features: {
 *     cache: true,
 *     seo: true,
 *     relatedContent: true,
 *     languageSwitching: true
 *   }
 * })
 * 
 * // Initialize for specific environment
 * await initializeCMS({
 *   features: {
 *     cache: process.env.NODE_ENV === 'production',
 *     seo: process.env.NODE_ENV === 'production'
 *   }
 * })
 * ```
 */
export async function initializeCMS(config?: Partial<CMSConfig>): Promise<void> {
  try {
    // Get default configuration and merge with provided config
    const defaultConfig = getDefaultCMSConfig()
    const finalConfig: CMSConfig = {
      ...defaultConfig,
      ...config,
      features: {
        ...defaultConfig.features,
        ...(config?.features || {})
      }
    }
    
    // Initialize the singleton CMS service
    await cms.initialize(finalConfig)
    
    console.log('CMS service initialized successfully')
  } catch (error) {
    console.error('Failed to initialize CMS service:', error)
    throw error
  }
}

/**
 * Create CMS service with validation
 * 
 * Enhanced factory function that performs comprehensive validation
 * of the CMS configuration before creating the service instance.
 * This function provides detailed error messages for configuration
 * issues and ensures that the CMS service is properly set up.
 * 
 * @param config - CMS configuration to validate and use
 * @returns Validated and configured CMS service instance
 * @throws {Error} With detailed validation error messages
 * 
 * @example
 * ```typescript
 * try {
 *   const cmsService = createValidatedCMSService(config)
 *   await cmsService.initialize(config)
 * } catch (error) {
 *   console.error('CMS service creation failed:', error.message)
 * }
 * ```
 */
export function createValidatedCMSService(config: CMSConfig): CMSService {
  // Comprehensive configuration validation
  const errors: string[] = []

  if (!config) {
    errors.push('Configuration object is required')
  } else {
    if (!config.provider) {
      errors.push('CMS provider is required')
    } else if (!['contentlayer2', 'strapi', 'sanity', 'contentful', 'nextjs-mdx'].includes(config.provider)) {
      errors.push(`Invalid CMS provider: ${config.provider}`)
    }

    if (!config.contentTypes || config.contentTypes.length === 0) {
      errors.push('At least one content type must be specified')
    }

    if (!config.defaultLocale) {
      errors.push('Default locale is required')
    }

    if (!config.supportedLocales || config.supportedLocales.length === 0) {
      errors.push('At least one supported locale must be specified')
    } else if (!config.supportedLocales.includes(config.defaultLocale)) {
      errors.push('Default locale must be included in supported locales')
    }

    if (!config.features) {
      errors.push('Features configuration is required')
    }
  }

  if (errors.length > 0) {
    throw new Error(`CMS configuration validation failed:\n${errors.join('\n')}`)
  }

  return createCMSService()
}

/**
 * Create CMS service for testing
 * 
 * Convenience factory function for creating a CMS service
 * specifically configured for testing environments. This includes
 * mock data support, minimal caching, and testing-friendly features.
 * 
 * @param options - Testing-specific options
 * @returns CMS service configured for testing
 * 
 * @example
 * ```typescript
 * const testCMS = createTestCMSService({
 *   mockData: true,
 *   enableDebugLogs: false
 * })
 * 
 * await testCMS.initialize(testConfig)
 * ```
 */
export function createTestCMSService(options?: {
  mockData?: boolean
  enableDebugLogs?: boolean
  contentTypes?: string[]
}): CMSService {
  const service = createCMSService()
  
  // Test services can be configured with specific test options
  // This allows for isolated testing without affecting production configuration
  
  return service
}

/**
 * Get CMS service instance
 * 
 * Utility function to get the singleton CMS service instance.
 * This function includes initialization status checking and
 * provides helpful error messages for common issues.
 * 
 * @param requireInitialized - Whether to require the service to be initialized
 * @returns The singleton CMS service instance
 * @throws {Error} When service is not initialized and requireInitialized is true
 * 
 * @example
 * ```typescript
 * // Get service (may not be initialized)
 * const service = getCMSService()
 * 
 * // Get service and ensure it's initialized
 * const initializedService = getCMSService(true)
 * ```
 */
export function getCMSService(requireInitialized: boolean = false): CMSService {
  if (requireInitialized && !cms.isInitialized()) {
    throw new Error('CMS service is not initialized. Call initializeCMS() first.')
  }
  
  return cms
}

/**
 * Reset CMS service
 * 
 * Utility function for resetting the CMS service state.
 * This is primarily useful for testing scenarios where
 * you need to reset the service between tests.
 * 
 * @example
 * ```typescript
 * // In test teardown
 * resetCMSService()
 * ```
 */
export function resetCMSService(): void {
  // Note: Since cms is a const, we can't reassign it
  // The actual reset would need to be implemented as a method on CMSService
  // This function serves as a placeholder for future implementation
  console.warn('CMS service reset not yet implemented')
}
