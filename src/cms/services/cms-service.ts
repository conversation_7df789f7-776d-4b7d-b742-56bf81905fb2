/**
 * CMS Service Implementation
 * 
 * This module provides the main CMS service class that coordinates between
 * different CMS providers and provides a unified interface for content
 * management operations. The service abstracts away the complexity of
 * different CMS providers and provides a consistent API for the application.
 * 
 * The CMSService class acts as a facade pattern implementation, providing
 * a simplified interface to the complex subsystem of CMS providers while
 * handling initialization, error management, and provider coordination.
 * 
 * Key Features:
 * - Provider-agnostic content operations
 * - Automatic error handling and logging
 * - Type-safe content queries and mutations
 * - Multi-language content support
 * - Static generation support for Next.js
 * - Provider capability detection and fallbacks
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */

import type { 
  ContentProvider, 
  ContentItem, 
  ContentType, 
  QueryOptions,
  ContentMetadata,
  LanguageVersion,
  CMSConfig
} from '../types'

// Import available providers
import { Contentlayer2Provider } from '../providers/contentlayer2/provider'

/**
 * CMS Service Class
 * 
 * Main service class that coordinates between different CMS providers
 * and provides a unified interface for content operations. This class
 * handles provider initialization, caching, and error management.
 * 
 * The service follows the facade pattern to provide a simplified interface
 * to the complex CMS provider ecosystem. It ensures that all content
 * operations are consistent regardless of the underlying provider.
 * 
 * @example
 * ```typescript
 * const cmsService = new CMSService()
 * 
 * await cmsService.initialize({
 *   provider: 'contentlayer2',
 *   contentTypes: ['blog', 'product'],
 *   defaultLocale: 'en',
 *   supportedLocales: ['en', 'zh'],
 *   features: { cache: true, seo: true }
 * })
 * 
 * const blogPost = await cmsService.getContent<BlogContent>('blog', 'my-post', 'en')
 * ```
 */
export class CMSService {
  private provider: ContentProvider | null = null
  private config: CMSConfig | null = null
  private initialized = false

  /**
   * Initialize CMS with specified provider
   * 
   * Sets up the CMS service with the specified provider and configuration.
   * This method must be called before any content operations can be performed.
   * The initialization process includes provider instantiation, configuration
   * validation, and capability detection.
   * 
   * @param config - CMS configuration including provider selection and feature flags
   * @throws {Error} When provider initialization fails or configuration is invalid
   * 
   * @example
   * ```typescript
   * await cmsService.initialize({
   *   provider: 'contentlayer2',
   *   contentTypes: ['blog', 'product', 'case-study'],
   *   defaultLocale: 'en',
   *   supportedLocales: ['en', 'zh'],
   *   features: {
   *     cache: true,
   *     seo: true,
   *     relatedContent: true,
   *     languageSwitching: true
   *   }
   * })
   * ```
   */
  async initialize(config: CMSConfig): Promise<void> {
    try {
      this.config = config
      
      // Initialize the specified provider
      switch (config.provider) {
        case 'contentlayer2':
          this.provider = new Contentlayer2Provider()
          break
        
        case 'strapi':
          // TODO: Implement Strapi provider
          throw new Error('Strapi provider not yet implemented')
        
        case 'sanity':
          // TODO: Implement Sanity provider
          throw new Error('Sanity provider not yet implemented')
        
        case 'contentful':
          // TODO: Implement Contentful provider
          throw new Error('Contentful provider not yet implemented')
        
        case 'nextjs-mdx':
          // TODO: Implement Next.js MDX provider
          throw new Error('Next.js MDX provider not yet implemented')
        
        default:
          throw new Error(`Unknown CMS provider: ${config.provider}`)
      }
      
      this.initialized = true
      console.log(`CMS initialized with provider: ${config.provider}`)
      
    } catch (error) {
      console.error('Failed to initialize CMS:', error)
      throw error
    }
  }

  /**
   * Get single content item
   * 
   * Retrieves a specific content item by type, slug, and language.
   * This method provides type-safe access to individual content items
   * with automatic error handling and logging.
   * 
   * The method includes comprehensive error handling and will return null
   * if the content is not found rather than throwing an error, making it
   * safe to use in conditional rendering scenarios.
   * 
   * @param type - Content type (blog, product, case-study)
   * @param slug - Content slug identifier
   * @param locale - Language code (en, zh)
   * @returns Promise resolving to content item or null if not found
   * 
   * @example
   * ```typescript
   * const blogPost = await cmsService.getContent<BlogContent>('blog', 'my-post', 'en')
   * if (blogPost) {
   *   console.log(blogPost.title)
   * } else {
   *   console.log('Blog post not found')
   * }
   * ```
   */
  async getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null> {
    this.ensureInitialized()
    
    try {
      const content = await this.provider!.getContent<T>(type, slug, locale)
      
      if (!content) {
        console.warn(`Content not found: ${type}/${locale}/${slug}`)
      }
      
      return content
    } catch (error) {
      console.error(`Error fetching content: ${type}/${locale}/${slug}`, error)
      return null
    }
  }

  /**
   * Get content list with filtering and sorting
   *
   * Retrieves a filtered and sorted list of content items for a specific
   * type and language. Supports various query options including featured
   * content filtering, pagination, and custom sorting.
   *
   * This method is optimized for listing pages, search results, and
   * content discovery features. It provides comprehensive filtering
   * and sorting capabilities while maintaining performance.
   *
   * @param type - Content type to query
   * @param locale - Language code for filtering
   * @param options - Query options for filtering and sorting
   * @returns Promise resolving to array of content items
   *
   * @example
   * ```typescript
   * // Get featured blog posts
   * const featuredPosts = await cmsService.getContentList<BlogContent>('blog', 'en', {
   *   featured: true,
   *   sortBy: 'publishedAt',
   *   order: 'desc',
   *   limit: 5
   * })
   *
   * // Get products by tag
   * const products = await cmsService.getContentList<ProductContent>('product', 'en', {
   *   tags: ['featured', 'new'],
   *   limit: 10
   * })
   * ```
   */
  async getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options: QueryOptions = {}
  ): Promise<T[]> {
    this.ensureInitialized()

    try {
      const content = await this.provider!.getContentList<T>(type, locale, options)

      console.log(`Retrieved ${content.length} ${type} items for locale: ${locale}`)

      return content
    } catch (error) {
      console.error(`Error fetching content list: ${type}/${locale}`, error)
      return []
    }
  }

  /**
   * Check if content exists
   *
   * Verifies whether a specific content item exists for the given
   * type, slug, and language combination. This is useful for
   * language switching, content availability checks, and 404 handling.
   *
   * This method is optimized for quick existence checks without
   * fetching the full content, making it ideal for validation
   * and conditional logic scenarios.
   *
   * @param type - Content type to check
   * @param slug - Content slug to verify
   * @param locale - Language code to check
   * @returns Promise resolving to boolean indicating existence
   *
   * @example
   * ```typescript
   * const exists = await cmsService.contentExists('blog', 'my-post', 'zh')
   * if (exists) {
   *   // Show language switch option
   * } else {
   *   // Hide language switch for this content
   * }
   * ```
   */
  async contentExists(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<boolean> {
    this.ensureInitialized()

    try {
      return await this.provider!.contentExists(type, slug, locale)
    } catch (error) {
      console.error(`Error checking content existence: ${type}/${locale}/${slug}`, error)
      return false
    }
  }

  /**
   * Get content title
   *
   * Retrieves the title of a specific content item. This is a
   * convenience method for quick title access without fetching
   * the entire content object, useful for navigation, breadcrumbs,
   * and page titles.
   *
   * @param type - Content type
   * @param slug - Content slug
   * @param locale - Language code
   * @returns Promise resolving to title string or null
   *
   * @example
   * ```typescript
   * const title = await cmsService.getContentTitle('blog', 'my-post', 'en')
   * if (title) {
   *   document.title = title
   * }
   * ```
   */
  async getContentTitle(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<string | null> {
    this.ensureInitialized()

    try {
      return await this.provider!.getContentTitle(type, slug, locale)
    } catch (error) {
      console.error(`Error fetching content title: ${type}/${locale}/${slug}`, error)
      return null
    }
  }

  /**
   * Get content metadata
   *
   * Extracts metadata information from a content item including
   * reading time, word count, and other computed properties.
   * This metadata is useful for SEO, user experience features,
   * and content analytics.
   *
   * @param type - Content type
   * @param slug - Content slug
   * @param locale - Language code
   * @returns Promise resolving to content metadata
   *
   * @example
   * ```typescript
   * const metadata = await cmsService.getContentMetadata('blog', 'my-post', 'en')
   * if (metadata) {
   *   console.log(`Reading time: ${metadata.readingTime} minutes`)
   *   console.log(`Word count: ${metadata.wordCount}`)
   * }
   * ```
   */
  async getContentMetadata(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<ContentMetadata | null> {
    this.ensureInitialized()

    try {
      return await this.provider!.getContentMetadata(type, slug, locale)
    } catch (error) {
      console.error(`Error fetching content metadata: ${type}/${locale}/${slug}`, error)
      return null
    }
  }

  /**
   * Get available language versions
   *
   * Finds all available language versions of a specific content item.
   * This is essential for implementing language switching functionality
   * and providing users with alternative language options.
   *
   * The method returns comprehensive information about each language
   * version including availability status, localized titles, and URLs.
   *
   * @param type - Content type
   * @param slug - Content slug to find versions for
   * @returns Promise resolving to array of language versions
   *
   * @example
   * ```typescript
   * const languages = await cmsService.getAvailableLanguages('blog', 'my-post')
   * languages.forEach(lang => {
   *   if (lang.available) {
   *     console.log(`Available in ${lang.lang}: ${lang.title}`)
   *   }
   * })
   * ```
   */
  async getAvailableLanguages(
    type: ContentType,
    slug: string
  ): Promise<LanguageVersion[]> {
    this.ensureInitialized()

    try {
      return await this.provider!.getAvailableLanguages(type, slug)
    } catch (error) {
      console.error(`Error fetching language versions: ${type}/${slug}`, error)
      return []
    }
  }

  /**
   * Get related content
   *
   * Finds content items related to the current item based on tags,
   * content type, and other similarity metrics. This is useful for
   * implementing "related posts", "you might also like", and content
   * discovery features.
   *
   * The method includes intelligent fallback logic when the provider
   * doesn't support native related content functionality, using
   * recent content as an alternative.
   *
   * @param type - Content type
   * @param currentSlug - Current content slug to find related items for
   * @param locale - Language code
   * @param limit - Maximum number of related items to return
   * @returns Promise resolving to array of related content items
   *
   * @example
   * ```typescript
   * const related = await cmsService.getRelatedContent<BlogContent>(
   *   'blog',
   *   'current-post',
   *   'en',
   *   3
   * )
   *
   * related.forEach(post => {
   *   console.log(`Related: ${post.title}`)
   * })
   * ```
   */
  async getRelatedContent<T extends ContentItem>(
    type: ContentType,
    currentSlug: string,
    locale: string,
    limit: number = 3
  ): Promise<T[]> {
    this.ensureInitialized()

    try {
      if (this.provider!.getRelatedContent) {
        return await this.provider!.getRelatedContent<T>(type, currentSlug, locale, limit)
      }

      // Fallback: return recent content if provider doesn't support related content
      const recentContent = await this.getContentList<T>(type, locale, {
        sortBy: 'publishedAt',
        order: 'desc',
        limit: limit + 1
      })

      // Filter out current content
      return recentContent.filter(item => item.slug !== currentSlug).slice(0, limit)

    } catch (error) {
      console.error(`Error fetching related content: ${type}/${locale}/${currentSlug}`, error)
      return []
    }
  }

  /**
   * Get content for static generation
   *
   * Retrieves all content items of a specific type for static generation.
   * This method is optimized for build-time usage and is used by Next.js
   * generateStaticParams and other static generation features.
   *
   * The method is designed to handle large datasets efficiently and
   * provides all content across all locales for comprehensive static
   * site generation.
   *
   * @param type - Content type to retrieve
   * @returns Promise resolving to array of all content items
   *
   * @example
   * ```typescript
   * // In generateStaticParams
   * const allBlogs = await cmsService.getContentForStaticGeneration<BlogContent>('blog')
   * return allBlogs.map(blog => ({
   *   locale: blog.lang,
   *   slug: blog.slug
   * }))
   * ```
   */
  async getContentForStaticGeneration<T extends ContentItem>(
    type: ContentType
  ): Promise<T[]> {
    this.ensureInitialized()

    try {
      return await this.provider!.getContentForStaticGeneration<T>(type)
    } catch (error) {
      console.error(`Error fetching content for static generation: ${type}`, error)
      return []
    }
  }

  /**
   * Get all content slugs for static generation
   *
   * Retrieves all slug and locale combinations for a specific content type.
   * This is used by Next.js generateStaticParams to create static routes
   * for all available content in all languages.
   *
   * This method is optimized for build-time performance and provides
   * the minimal data needed for route generation.
   *
   * @param type - Content type to get slugs for
   * @returns Promise resolving to array of slug/locale combinations
   *
   * @example
   * ```typescript
   * // In generateStaticParams
   * const slugs = await cmsService.getAllContentSlugs('blog')
   * return slugs.map(({ locale, slug }) => ({
   *   locale,
   *   slug
   * }))
   * ```
   */
  async getAllContentSlugs(
    type: ContentType
  ): Promise<Array<{ locale: string; slug: string }>> {
    this.ensureInitialized()

    try {
      return await this.provider!.getAllContentSlugs(type)
    } catch (error) {
      console.error(`Error fetching content slugs: ${type}`, error)
      return []
    }
  }

  /**
   * Get provider information
   *
   * Returns information about the currently active CMS provider
   * including name, version, and capabilities. This is useful for
   * debugging, monitoring, and feature detection.
   *
   * @returns Provider information object or null if not initialized
   *
   * @example
   * ```typescript
   * const providerInfo = cmsService.getProviderInfo()
   * if (providerInfo) {
   *   console.log(`Using ${providerInfo.name} v${providerInfo.version}`)
   * }
   * ```
   */
  getProviderInfo(): { name: string; version: string } | null {
    if (!this.provider) return null

    return {
      name: this.provider.name,
      version: this.provider.version
    }
  }

  /**
   * Get CMS configuration
   *
   * Returns the current CMS configuration including provider
   * settings, feature flags, and other options. This is useful
   * for debugging and conditional feature implementation.
   *
   * @returns Current CMS configuration or null if not initialized
   *
   * @example
   * ```typescript
   * const config = cmsService.getConfig()
   * if (config?.features.cache) {
   *   // Use caching features
   * }
   * ```
   */
  getConfig(): CMSConfig | null {
    return this.config
  }

  /**
   * Check if CMS is initialized
   *
   * Returns whether the CMS service has been properly initialized
   * and is ready to handle content operations. This is useful for
   * conditional logic and error prevention.
   *
   * @returns Boolean indicating initialization status
   *
   * @example
   * ```typescript
   * if (!cmsService.isInitialized()) {
   *   await cmsService.initialize(config)
   * }
   * ```
   */
  isInitialized(): boolean {
    return this.initialized && this.provider !== null
  }

  // Private helper methods

  /**
   * Ensure CMS is initialized
   *
   * Internal helper method that ensures the CMS service has been
   * properly initialized before performing any operations. This
   * method throws an error if the service is not ready.
   *
   * @private
   * @throws {Error} When CMS service is not initialized
   */
  private ensureInitialized(): void {
    if (!this.initialized || !this.provider) {
      throw new Error('CMS service not initialized. Call initialize() first.')
    }
  }
}
