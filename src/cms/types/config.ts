/**
 * CMS Configuration Types
 * 
 * This module defines configuration interfaces and types for the CMS system.
 * The configuration system provides flexible setup options for different
 * CMS providers, feature flags, performance settings, and deployment
 * environments.
 * 
 * The configuration types support multiple CMS backends and provide
 * fine-grained control over system behavior, caching strategies,
 * and feature enablement.
 * 
 * Key Features:
 * - Multi-provider CMS configuration
 * - Feature flag system for optional capabilities
 * - Performance and caching configuration
 * - Multilingual content support settings
 * - Environment-specific configuration options
 * - Type-safe configuration validation
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */

import type { ContentType } from './content'

/**
 * Supported CMS Provider Types
 * 
 * Defines the available CMS providers that can be configured
 * in the system. Each provider has its own specific configuration
 * requirements and capabilities.
 * 
 * @example
 * ```typescript
 * const provider: CMSProviderType = 'contentlayer2'
 * ```
 */
export type CMSProviderType = 
  | 'contentlayer2'  // File-based CMS with TypeScript support
  | 'strapi'         // Headless CMS with REST/GraphQL API
  | 'sanity'         // Real-time headless CMS
  | 'contentful'     // Cloud-based headless CMS
  | 'nextjs-mdx'     // Next.js built-in MDX support



/**
 * CMS Feature Configuration
 * 
 * Defines which optional features are enabled in the CMS system.
 * Feature flags allow for gradual rollout of new capabilities
 * and environment-specific feature sets.
 * 
 * @example
 * ```typescript
 * const features: CMSFeatures = {
 *   cache: true,
 *   seo: true,
 *   relatedContent: false,
 *   languageSwitching: true
 * }
 * ```
 */
export interface CMSFeatures {
  /** Enable SEO data generation and optimization */
  seo: boolean
  /** Enable related content suggestions */
  relatedContent: boolean
  /** Enable language switching functionality */
  languageSwitching: boolean
}



/**
 * SEO Configuration
 *
 * Defines SEO-related settings and optimization options.
 * This configuration controls how SEO data is generated
 * and what optimization features are enabled.
 *
 * @example
 * ```typescript
 * const seoConfig: CMSSEOConfig = {
 *   generateSitemap: true,
 *   generateRobotsTxt: true,
 *   defaultMetaDescription: 'Default site description',
 *   openGraphDefaults: {
 *     siteName: 'My Website',
 *     type: 'website'
 *   }
 * }
 * ```
 */
export interface CMSSEOConfig {
  /** Generate XML sitemap automatically */
  generateSitemap: boolean
  /** Generate robots.txt file */
  generateRobotsTxt: boolean
  /** Default meta description for pages without one */
  defaultMetaDescription?: string
  /** Default Open Graph properties */
  openGraphDefaults?: {
    siteName?: string
    type?: 'website' | 'article'
    image?: string
  }
}

/**
 * Partial SEO Configuration
 *
 * Used for environment-specific overrides where not all
 * SEO configuration properties need to be specified.
 */
export interface PartialCMSSEOConfig {
  /** Generate XML sitemap automatically */
  generateSitemap?: boolean
  /** Generate robots.txt file */
  generateRobotsTxt?: boolean
  /** Default meta description for pages without one */
  defaultMetaDescription?: string
  /** Default Open Graph properties */
  openGraphDefaults?: {
    siteName?: string
    type?: 'website' | 'article'
    image?: string
  }
}

/**
 * Multilingual Configuration
 * 
 * Defines settings for multilingual content support
 * including default locale, supported languages, and
 * URL structure preferences.
 * 
 * @example
 * ```typescript
 * const i18nConfig: CMSI18nConfig = {
 *   defaultLocale: 'en',
 *   supportedLocales: ['en', 'zh', 'es'],
 *   urlStrategy: 'prefix',
 *   fallbackToDefault: true
 * }
 * ```
 */
export interface CMSI18nConfig {
  /** Default language locale */
  defaultLocale: string
  /** Array of supported language locales */
  supportedLocales: string[]
  /** URL structure strategy for multilingual content */
  urlStrategy: 'prefix' | 'domain' | 'subdomain'
  /** Whether to fallback to default locale for missing translations */
  fallbackToDefault: boolean
}

/**
 * Performance Configuration
 *
 * Defines performance-related settings including
 * optimization options, resource limits, and
 * build-time configurations.
 *
 * @example
 * ```typescript
 * const perfConfig: CMSPerformanceConfig = {
 *   preloadCriticalContent: true,
 *   lazyLoadImages: true,
 *   optimizeImages: true,
 *   maxConcurrentRequests: 10
 * }
 * ```
 */
export interface CMSPerformanceConfig {
  /** Preload critical content for faster initial page loads */
  preloadCriticalContent: boolean
  /** Enable lazy loading for images */
  lazyLoadImages: boolean
  /** Optimize images automatically */
  optimizeImages: boolean
  /** Maximum number of concurrent content requests */
  maxConcurrentRequests: number
}

/**
 * Partial Performance Configuration
 *
 * Used for environment-specific overrides where not all
 * performance configuration properties need to be specified.
 */
export interface PartialCMSPerformanceConfig {
  /** Preload critical content for faster initial page loads */
  preloadCriticalContent?: boolean
  /** Enable lazy loading for images */
  lazyLoadImages?: boolean
  /** Optimize images automatically */
  optimizeImages?: boolean
  /** Maximum number of concurrent content requests */
  maxConcurrentRequests?: number
}

/**
 * Development Configuration
 *
 * Defines development-specific settings including
 * debugging options, hot reload behavior, and
 * development server configurations.
 *
 * @example
 * ```typescript
 * const devConfig: CMSDevConfig = {
 *   enableDebugLogs: true,
 *   hotReload: true,
 *   mockData: false,
 *   validateContent: true
 * }
 * ```
 */
export interface CMSDevConfig {
  /** Enable detailed debug logging */
  enableDebugLogs: boolean
  /** Enable hot reload for content changes */
  hotReload: boolean
  /** Use mock data instead of real CMS content */
  mockData: boolean
  /** Validate content structure and types */
  validateContent: boolean
}

/**
 * Partial Development Configuration
 *
 * Used for environment-specific overrides where not all
 * development configuration properties need to be specified.
 */
export interface PartialCMSDevConfig {
  /** Enable detailed debug logging */
  enableDebugLogs?: boolean
  /** Enable hot reload for content changes */
  hotReload?: boolean
  /** Use mock data instead of real CMS content */
  mockData?: boolean
  /** Validate content structure and types */
  validateContent?: boolean
}

/**
 * CMS Configuration Interface
 *
 * Defines the complete configuration options for the CMS system
 * including provider selection, content settings, feature flags,
 * and performance optimizations.
 *
 * This is the main configuration interface that applications
 * use to set up the CMS system according to their requirements.
 *
 * @example
 * ```typescript
 * const cmsConfig: CMSConfig = {
 *   provider: 'contentlayer2',
 *   contentTypes: ['blog', 'product', 'case-study'],
 *   defaultLocale: 'en',
 *   supportedLocales: ['en', 'zh'],
 *   features: {
 *     cache: true,
 *     seo: true,
 *     relatedContent: false,
 *     languageSwitching: true
 *   },
 *   cache: {
 *     enabled: true,
 *     ttl: 3600,
 *     strategy: 'memory'
 *   }
 * }
 * ```
 */
export interface CMSConfig {
  // Provider configuration
  /** CMS provider to use */
  provider: CMSProviderType

  // Content configuration
  /** Array of content types to support */
  contentTypes: ContentType[]
  /** Default language locale */
  defaultLocale: string
  /** Array of supported language locales */
  supportedLocales: string[]

  // Feature flags
  /** Feature enablement configuration */
  features: CMSFeatures

  // Optional advanced configurations
  /** SEO configuration (when SEO feature is enabled) */
  seo?: CMSSEOConfig
  /** Internationalization configuration */
  i18n?: CMSI18nConfig
  /** Performance optimization configuration */
  performance?: CMSPerformanceConfig
  /** Development-specific configuration */
  development?: CMSDevConfig
}

/**
 * Environment Configuration Override
 *
 * Defines configuration overrides for specific environments.
 * This interface uses partial types to allow selective overriding
 * of configuration properties without requiring all fields.
 */
export interface CMSEnvironmentConfig {
  /** Feature enablement overrides */
  features?: Partial<CMSFeatures>
  /** SEO configuration overrides */
  seo?: PartialCMSSEOConfig
  /** Internationalization configuration overrides */
  i18n?: Partial<CMSI18nConfig>
  /** Performance configuration overrides */
  performance?: PartialCMSPerformanceConfig
  /** Development configuration overrides */
  development?: PartialCMSDevConfig
}

/**
 * Environment-Specific Configuration Presets
 * 
 * Predefined configuration presets for different deployment
 * environments. These presets provide sensible defaults
 * for common deployment scenarios.
 */

/**
 * Development Environment Configuration
 *
 * Optimized for development with debugging enabled,
 * shorter cache times, and development-friendly features.
 */
export const developmentConfig: CMSEnvironmentConfig = {
  features: {
    seo: false,
    relatedContent: false,
    languageSwitching: true
  },
  development: {
    enableDebugLogs: true,
    hotReload: true,
    mockData: false,
    validateContent: true
  }
}

/**
 * Production Environment Configuration
 *
 * Optimized for production with performance features
 * enabled, longer cache times, and production-ready settings.
 */
export const productionConfig: CMSEnvironmentConfig = {
  features: {
    seo: true,
    relatedContent: true,
    languageSwitching: true
  },
  seo: {
    generateSitemap: true,
    generateRobotsTxt: true
  },
  performance: {
    preloadCriticalContent: true,
    lazyLoadImages: true,
    optimizeImages: true,
    maxConcurrentRequests: 20
  },
  development: {
    enableDebugLogs: false,
    hotReload: false,
    mockData: false,
    validateContent: false
  }
}

/**
 * Testing Environment Configuration
 *
 * Optimized for testing with mock data support,
 * minimal caching, and testing-friendly features.
 */
export const testingConfig: CMSEnvironmentConfig = {
  features: {
    seo: false,
    relatedContent: false,
    languageSwitching: false
  },
  development: {
    enableDebugLogs: false,
    hotReload: false,
    mockData: true,
    validateContent: true
  }
}

/**
 * Configuration Utility Functions
 */

/**
 * Merge configuration with environment preset
 *
 * Utility function to merge a base configuration with
 * environment-specific presets for easy configuration
 * management across different deployment environments.
 *
 * This function uses proper type-safe merging without type assertions,
 * ensuring that all merged configurations maintain type safety.
 *
 * @param baseConfig - Base CMS configuration
 * @param environment - Target environment
 * @returns Merged configuration
 *
 * @example
 * ```typescript
 * const config = mergeConfigWithEnvironment(baseConfig, 'production')
 * ```
 */
export function mergeConfigWithEnvironment(
  baseConfig: CMSConfig,
  environment: 'development' | 'production' | 'testing'
): CMSConfig {
  const envConfig = environment === 'development' ? developmentConfig
    : environment === 'production' ? productionConfig
    : testingConfig

  return {
    ...baseConfig,
    features: {
      ...baseConfig.features,
      ...(envConfig.features || {})
    },
    seo: mergeOptionalConfig(baseConfig.seo, envConfig.seo),
    i18n: mergeOptionalConfig(baseConfig.i18n, envConfig.i18n),
    performance: mergeOptionalConfig(baseConfig.performance, envConfig.performance),
    development: mergeOptionalConfig(baseConfig.development, envConfig.development)
  }
}

/**
 * Type-safe merge helper for optional configuration objects
 *
 * Merges two optional configuration objects while maintaining type safety.
 * This helper eliminates the need for type assertions by properly handling
 * the merging of partial and complete configuration objects.
 *
 * @param base - Base configuration object (may be undefined)
 * @param override - Override configuration object (may be undefined or partial)
 * @returns Merged configuration object or undefined
 */
function mergeOptionalConfig<T>(
  base: T | undefined,
  override: Partial<T> | undefined
): T | undefined {
  if (!base && !override) {
    return undefined
  }

  if (!base && override) {
    // If we only have override (partial), we can't create a complete T
    // This should be handled by the caller to provide defaults
    return undefined
  }

  if (base && !override) {
    return base
  }

  // Both exist, merge them
  return {
    ...base,
    ...override
  } as T
}
