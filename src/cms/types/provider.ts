/**
 * Content Provider Interface
 * 
 * This module defines the contract that all CMS providers must implement
 * to ensure consistent functionality across different CMS backends.
 * The provider interface abstracts away the specific implementation
 * details of different CMS systems while providing a unified API.
 * 
 * The interface supports various CMS operations including content
 * retrieval, static generation, metadata access, and multilingual
 * content management. This abstraction enables seamless switching
 * between different CMS providers without changing application code.
 * 
 * Key Features:
 * - Provider-agnostic content operations
 * - Support for static site generation
 * - Multilingual content handling
 * - Metadata and utility operations
 * - Optional advanced features (related content)
 * - Consistent error handling patterns
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */

import type {
  ContentItem,
  ContentType,
  ContentMetadata,
  LanguageVersion
} from './content'

/**
 * Query Options Interface
 * 
 * Defines the available options for querying content from
 * CMS providers. These options provide flexible filtering,
 * sorting, and pagination capabilities that work across
 * different CMS backends.
 * 
 * The query options are designed to be provider-agnostic
 * while supporting common content management use cases
 * such as featured content, tag-based filtering, and
 * author-specific queries.
 * 
 * @example
 * ```typescript
 * const options: QueryOptions = {
 *   featured: true,
 *   tags: ['typescript', 'tutorial'],
 *   sortBy: 'publishedAt',
 *   order: 'desc',
 *   limit: 10,
 *   offset: 0
 * }
 * 
 * const content = await provider.getContentList('blog', 'en', options)
 * ```
 */
export interface QueryOptions {
  // Filtering options
  /** Filter by featured status */
  featured?: boolean
  /** Filter by tags (content must have all specified tags) */
  tags?: string[]
  /** Filter by author name or identifier */
  author?: string
  
  // Sorting options
  /** Field to sort by */
  sortBy?: 'title' | 'publishedAt' | 'createdAt' | 'featured'
  /** Sort order (ascending or descending) */
  order?: 'asc' | 'desc'
  
  // Pagination options
  /** Maximum number of items to return */
  limit?: number
  /** Number of items to skip (for pagination) */
  offset?: number
}

/**
 * Content Provider Interface
 * 
 * Defines the contract that all CMS providers must implement.
 * This interface ensures that different CMS backends can be
 * used interchangeably while providing consistent functionality.
 * 
 * The provider interface is designed to support both dynamic
 * content delivery and static site generation workflows.
 * It includes methods for content retrieval, metadata access,
 * and utility operations that are common across CMS systems.
 * 
 * Providers should implement all required methods and may
 * optionally implement advanced features like related content
 * suggestions based on their capabilities.
 * 
 * @example
 * ```typescript
 * class MyCustomProvider implements ContentProvider {
 *   readonly name = 'my-custom-cms'
 *   readonly version = '1.0.0'
 *   
 *   async getContent<T extends ContentItem>(
 *     type: ContentType,
 *     slug: string,
 *     locale: string
 *   ): Promise<T | null> {
 *     // Implementation specific to your CMS
 *   }
 *   
 *   // ... implement other required methods
 * }
 * ```
 */
export interface ContentProvider {
  // Provider identification
  /** Human-readable name of the CMS provider */
  readonly name: string
  /** Version of the provider implementation */
  readonly version: string
  
  // Core content operations
  
  /**
   * Retrieve a single content item by type, slug, and locale
   * 
   * This is the primary method for fetching individual content
   * items. It should return null if the content is not found
   * rather than throwing an error.
   * 
   * @param type - The content type to retrieve
   * @param slug - The unique slug identifier
   * @param locale - The language locale
   * @returns The content item or null if not found
   * 
   * @example
   * ```typescript
   * const blogPost = await provider.getContent<BlogContent>('blog', 'my-post', 'en')
   * if (blogPost) {
   *   console.log(blogPost.title)
   * }
   * ```
   */
  getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null>

  /**
   * Retrieve a list of content items with optional filtering and sorting
   * 
   * This method supports flexible querying with filtering, sorting,
   * and pagination options. It should return an empty array if no
   * content matches the criteria.
   * 
   * @param type - The content type to list
   * @param locale - The language locale
   * @param options - Optional query parameters for filtering and sorting
   * @returns Array of content items matching the criteria
   * 
   * @example
   * ```typescript
   * const featuredPosts = await provider.getContentList<BlogContent>(
   *   'blog',
   *   'en',
   *   { featured: true, limit: 5 }
   * )
   * ```
   */
  getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: QueryOptions
  ): Promise<T[]>

  // Static generation support
  
  /**
   * Get all content for static site generation
   * 
   * This method is used during build time to retrieve all content
   * of a specific type for static site generation. It should return
   * content from all locales to support multilingual static sites.
   * 
   * @param type - The content type to retrieve
   * @returns Array of all content items of the specified type
   * 
   * @example
   * ```typescript
   * const allBlogs = await provider.getContentForStaticGeneration<BlogContent>('blog')
   * // Use for generating static pages at build time
   * ```
   */
  getContentForStaticGeneration<T extends ContentItem>(
    type: ContentType
  ): Promise<T[]>

  /**
   * Get all content slugs for static path generation
   * 
   * This method returns all available slug/locale combinations
   * for a content type, which is used to generate static paths
   * in frameworks like Next.js.
   * 
   * @param type - The content type to get slugs for
   * @returns Array of slug/locale combinations
   * 
   * @example
   * ```typescript
   * const paths = await provider.getAllContentSlugs('blog')
   * // Returns: [{ locale: 'en', slug: 'post-1' }, { locale: 'zh', slug: 'post-1' }, ...]
   * ```
   */
  getAllContentSlugs(
    type: ContentType
  ): Promise<Array<{ locale: string; slug: string }>>
  
  /**
   * Check if content exists
   * 
   * Efficiently check whether a specific content item exists
   * without retrieving the full content. This is useful for
   * validation and conditional logic.
   * 
   * @param type - The content type
   * @param slug - The content slug
   * @param locale - The language locale
   * @returns True if the content exists, false otherwise
   * 
   * @example
   * ```typescript
   * const exists = await provider.contentExists('blog', 'my-post', 'en')
   * if (!exists) {
   *   throw new Error('Content not found')
   * }
   * ```
   */
  contentExists(
    type: ContentType, 
    slug: string, 
    locale: string
  ): Promise<boolean>
  
  // Metadata and utility operations
  
  /**
   * Get content title only
   * 
   * Efficiently retrieve just the title of a content item
   * without loading the full content. This is useful for
   * navigation, breadcrumbs, and other UI elements.
   * 
   * @param type - The content type
   * @param slug - The content slug
   * @param locale - The language locale
   * @returns The content title or null if not found
   * 
   * @example
   * ```typescript
   * const title = await provider.getContentTitle('blog', 'my-post', 'en')
   * // Use for page titles, navigation, etc.
   * ```
   */
  getContentTitle(
    type: ContentType, 
    slug: string, 
    locale: string
  ): Promise<string | null>
  
  /**
   * Get content metadata
   * 
   * Retrieve computed metadata for a content item including
   * word count, reading time, and other derived properties.
   * This metadata is often cached for performance.
   * 
   * @param type - The content type
   * @param slug - The content slug
   * @param locale - The language locale
   * @returns Content metadata or null if not found
   * 
   * @example
   * ```typescript
   * const metadata = await provider.getContentMetadata('blog', 'my-post', 'en')
   * if (metadata) {
   *   console.log(`Reading time: ${metadata.readingTime} minutes`)
   * }
   * ```
   */
  getContentMetadata(
    type: ContentType, 
    slug: string, 
    locale: string
  ): Promise<ContentMetadata | null>
  
  /**
   * Get available language versions
   * 
   * Retrieve information about all available language versions
   * of a specific content item. This is used for language
   * switching functionality and multilingual navigation.
   * 
   * @param type - The content type
   * @param slug - The content slug (language-agnostic)
   * @returns Array of available language versions
   * 
   * @example
   * ```typescript
   * const versions = await provider.getAvailableLanguages('blog', 'my-post')
   * // Use to build language switcher UI
   * ```
   */
  getAvailableLanguages(
    type: ContentType, 
    slug: string
  ): Promise<LanguageVersion[]>
  
  // Advanced operations (optional)
  
  /**
   * Get related content (optional feature)
   * 
   * Retrieve content items that are related to the current item
   * based on tags, categories, or other similarity metrics.
   * This is an optional feature that providers may implement
   * based on their capabilities.
   * 
   * @param type - The content type
   * @param currentSlug - The slug of the current content item
   * @param locale - The language locale
   * @param limit - Maximum number of related items to return
   * @returns Array of related content items
   * 
   * @example
   * ```typescript
   * const related = await provider.getRelatedContent?.('blog', 'current-post', 'en', 5)
   * // Display related articles section
   * ```
   */
  getRelatedContent?<T extends ContentItem>(
    type: ContentType,
    currentSlug: string,
    locale: string,
    limit?: number
  ): Promise<T[]>
}
