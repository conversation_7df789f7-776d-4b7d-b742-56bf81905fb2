/**
 * CMS Types Module
 *
 * This module provides a unified interface for all CMS-related type
 * definitions and exports. It follows the index.ts design principle
 * by only containing re-exports and no implementation code.
 *
 * All concrete type definitions and implementations are located in
 * separate, focused modules for better maintainability and organization.
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */

// Export content-related types
export type {
  MDXContent,
  ContentType,
  BaseContentItem,
  BlogContent,
  ProductContent,
  CaseStudyContent,
  ContentItem,
  ContentMetadata,
  LanguageVersion,
  ContentTypeMap,
  ContentByType
} from './content'

// Export provider interface and query options
export type {
  ContentProvider,
  QueryOptions
} from './provider'

// Export configuration types
export type {
  CMSConfig,
  CMSProviderType,
  CMSFeatures,
  CMSSEOConfig,
  CMSI18nConfig,
  CMSPerformanceConfig,
  CMSDevConfig
} from './config'

export {
  developmentConfig,
  productionConfig,
  testingConfig,
  mergeConfigWithEnvironment
} from './config'

// Export error classes and utilities
export {
  CMSError,
  ContentNotFoundError,
  ProviderError,
  ContentValidationError,
  CacheError,
  CMSErrorCode,
  isCMSError,
  isContentNotFoundError,
  isProviderError,
  formatErrorResponse
} from './errors'

// Export SEO-related types
export type { SEOData } from './seo'
export * from './seo'

// Cache types removed for Cloudflare Workers optimization
// Previously exported cache types are no longer needed
