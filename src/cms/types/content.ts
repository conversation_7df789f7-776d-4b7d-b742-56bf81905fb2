/**
 * Content Type Definitions
 * 
 * This module provides comprehensive type definitions for content items
 * in the CMS system. These types are designed to be provider-agnostic,
 * allowing for seamless switching between different CMS backends while
 * maintaining type safety and consistent API interfaces.
 * 
 * The content types support various media formats, multilingual content,
 * and rich metadata for SEO and content management purposes.
 * 
 * Key Features:
 * - Provider-agnostic content interfaces
 * - Support for MDX content with code compilation
 * - Multilingual content support
 * - Rich media integration (images, videos)
 * - SEO-friendly metadata structure
 * - Extensible content type system
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */

/**
 * MDX Content Type
 *
 * Provider-agnostic MDX content type that abstracts away
 * the specific implementation details of different CMS providers.
 * This allows for consistent MDX handling regardless of the backend.
 * 
 * The MDX content includes both the raw markdown source and
 * the compiled JavaScript code for rendering, enabling efficient
 * server-side rendering and client-side hydration.
 * 
 * @example
 * ```typescript
 * const mdxContent: MDXContent = {
 *   raw: '# Hello World\n\nThis is **bold** text.',
 *   code: 'function MDXContent() { return React.createElement("h1", null, "Hello World"); }'
 * }
 * ```
 */
export interface MDXContent {
  /** Raw markdown/MDX source content */
  raw: string
  /** Compiled JavaScript code for rendering */
  code: string
}

/**
 * Content Type Identifiers
 * 
 * Defines the available content types in the system.
 * These identifiers are used throughout the CMS abstraction
 * layer to specify which type of content to operate on.
 * 
 * Each content type can have its own specific properties
 * while sharing the common base structure defined in
 * BaseContentItem.
 * 
 * @example
 * ```typescript
 * const contentType: ContentType = 'blog'
 * const products = await getContentList('product', 'en')
 * ```
 */
export type ContentType = 'blog' | 'product' | 'case-study'

/**
 * Base Content Item Interface
 * 
 * Defines the common structure that all content items must have
 * regardless of their specific type. This ensures consistency
 * across different content types and CMS providers.
 * 
 * The base interface includes essential fields for identification,
 * content storage, media handling, and publishing workflow.
 * All specific content types extend this interface.
 * 
 * @example
 * ```typescript
 * const baseContent: BaseContentItem = {
 *   slug: 'my-content',
 *   title: 'My Content Title',
 *   lang: 'en',
 *   url: '/en/my-content',
 *   body: { raw: '# Content', code: 'compiled...' },
 *   createdAt: '2024-01-01T00:00:00Z',
 *   featured: false
 * }
 * ```
 */
export interface BaseContentItem {
  // Core identification fields
  /** Unique slug identifier for the content */
  slug: string
  /** Display title of the content */
  title: string
  /** Language code (ISO 639-1) */
  lang: string
  /** Full URL path to the content */
  url: string
  
  // Content and metadata
  /** Optional short description or excerpt */
  description?: string
  /** Main content body in MDX format */
  body: MDXContent
  
  // Media and visual content
  /** URL to the cover/featured image */
  coverImage?: string
  /** URL to the author's profile image */
  authorImage?: string
  /** URL to an associated video */
  videoUrl?: string
  /** URL to the video thumbnail image */
  videoThumbnail?: string
  /** Duration of the video in human-readable format */
  videoDuration?: string
  
  // Publishing information
  /** Author name or identifier */
  author?: string
  /** Publication date in ISO 8601 format */
  publishedAt?: string
  /** Creation date in ISO 8601 format */
  createdAt: string
  /** Whether this content is featured/highlighted */
  featured: boolean
  /** Array of tags for categorization */
  tags?: string[]
}

/**
 * Blog Content Interface
 *
 * Represents blog post content with blog-specific properties.
 * Blog content typically focuses on articles, news, and
 * editorial content with strong emphasis on publishing
 * dates and author information.
 * 
 * @extends BaseContentItem
 * 
 * @example
 * ```typescript
 * const blogPost: BlogContent = {
 *   type: 'blog',
 *   slug: 'my-blog-post',
 *   title: 'Understanding TypeScript',
 *   lang: 'en',
 *   url: '/blog/understanding-typescript',
 *   body: { raw: '# TypeScript Guide', code: 'compiled...' },
 *   author: 'John Doe',
 *   publishedAt: '2024-01-15T10:00:00Z',
 *   createdAt: '2024-01-10T09:00:00Z',
 *   featured: true,
 *   tags: ['typescript', 'programming']
 * }
 * ```
 */
export interface BlogContent extends BaseContentItem {
  /** Content type identifier */
  type: 'blog'
}

/**
 * Product Content Interface
 *
 * Represents product or service content with product-specific
 * properties. Product content includes an optional icon field
 * for visual representation in product listings and catalogs.
 * 
 * @extends BaseContentItem
 * 
 * @example
 * ```typescript
 * const product: ProductContent = {
 *   type: 'product',
 *   slug: 'awesome-widget',
 *   title: 'Awesome Widget Pro',
 *   lang: 'en',
 *   url: '/products/awesome-widget',
 *   body: { raw: '# Product Description', code: 'compiled...' },
 *   icon: '/icons/widget.svg',
 *   createdAt: '2024-01-01T00:00:00Z',
 *   featured: false,
 *   tags: ['widget', 'productivity']
 * }
 * ```
 */
export interface ProductContent extends BaseContentItem {
  /** Content type identifier */
  type: 'product'
  /** Optional icon URL for product representation */
  icon?: string
}

/**
 * Case Study Content Interface
 *
 * Represents case study content showcasing projects,
 * implementations, or success stories. Case studies
 * typically include detailed project information and
 * outcomes.
 * 
 * @extends BaseContentItem
 * 
 * @example
 * ```typescript
 * const caseStudy: CaseStudyContent = {
 *   type: 'case-study',
 *   slug: 'client-success-story',
 *   title: 'How We Improved Client Performance by 300%',
 *   lang: 'en',
 *   url: '/case-studies/client-success-story',
 *   body: { raw: '# Case Study Details', code: 'compiled...' },
 *   createdAt: '2024-01-01T00:00:00Z',
 *   featured: true,
 *   tags: ['performance', 'optimization']
 * }
 * ```
 */
export interface CaseStudyContent extends BaseContentItem {
  /** Content type identifier */
  type: 'case-study'
}

/**
 * Content Item Union Type
 *
 * Represents any type of content item in the system.
 * This union type allows for type-safe handling of different
 * content types while maintaining their specific properties.
 * 
 * The union type enables polymorphic content handling where
 * functions can accept any content type and use type guards
 * or discriminated union patterns to handle type-specific logic.
 * 
 * @example
 * ```typescript
 * function processContent(content: ContentItem) {
 *   switch (content.type) {
 *     case 'blog':
 *       // Handle blog-specific logic
 *       break
 *     case 'product':
 *       // Handle product-specific logic (access content.icon)
 *       break
 *     case 'case-study':
 *       // Handle case study-specific logic
 *       break
 *   }
 * }
 * ```
 */
export type ContentItem = BlogContent | ProductContent | CaseStudyContent

/**
 * Content Metadata Interface
 * 
 * Represents metadata information about content items
 * including computed properties like reading time and
 * word count that are useful for SEO and UX features.
 * 
 * This metadata is typically computed from the content
 * body and cached for performance reasons.
 * 
 * @example
 * ```typescript
 * const metadata: ContentMetadata = {
 *   wordCount: 1250,
 *   readingTime: 5, // minutes
 *   publishedAt: '2024-01-15T10:00:00Z',
 *   updatedAt: '2024-01-16T14:30:00Z',
 *   author: 'John Doe',
 *   tags: ['typescript', 'programming', 'tutorial']
 * }
 * ```
 */
export interface ContentMetadata {
  /** Total word count in the content body */
  wordCount: number
  /** Estimated reading time in minutes */
  readingTime: number
  /** Publication date in ISO 8601 format */
  publishedAt?: string
  /** Last update date in ISO 8601 format */
  updatedAt?: string
  /** Author name or identifier */
  author?: string
  /** Array of content tags */
  tags: string[]
}

/**
 * Language Version Interface
 * 
 * Represents different language versions of the same content.
 * This is used for implementing language switching functionality
 * and providing users with alternative language options.
 * 
 * The interface includes availability status to handle cases
 * where content might not be translated to all supported languages.
 * 
 * @example
 * ```typescript
 * const languageVersions: LanguageVersion[] = [
 *   { lang: 'en', title: 'English Title', url: '/en/content', available: true },
 *   { lang: 'zh', title: '中文标题', url: '/zh/content', available: true },
 *   { lang: 'es', title: 'Título en Español', url: '/es/content', available: false }
 * ]
 * ```
 */
export interface LanguageVersion {
  /** Language code (ISO 639-1) */
  lang: string
  /** Localized title of the content */
  title: string
  /** URL to the localized version */
  url: string
  /** Whether this language version is available */
  available: boolean
}

/**
 * Utility Types
 *
 * Helper types for working with content and CMS operations
 * in a type-safe manner throughout the application.
 * 
 * These utility types provide convenient mappings between
 * content type identifiers and their corresponding interfaces.
 */

/**
 * Content Type Mapping
 * 
 * Maps content type identifiers to their corresponding
 * TypeScript interfaces for type-safe content handling.
 * 
 * @example
 * ```typescript
 * type BlogType = ContentTypeMap['blog'] // BlogContent
 * type ProductType = ContentTypeMap['product'] // ProductContent
 * ```
 */
export type ContentTypeMap = {
  blog: BlogContent
  product: ProductContent
  'case-study': CaseStudyContent
}

/**
 * Content By Type
 * 
 * Utility type that resolves a content type identifier
 * to its corresponding content interface.
 * 
 * @template T - The content type identifier
 * 
 * @example
 * ```typescript
 * type BlogContent = ContentByType<'blog'>
 * type ProductContent = ContentByType<'product'>
 * 
 * function getTypedContent<T extends ContentType>(type: T): ContentByType<T> {
 *   // Implementation with proper return type
 * }
 * ```
 */
export type ContentByType<T extends ContentType> = ContentTypeMap[T]
