/**
 * CMS Error Types and Classes
 * 
 * This module provides comprehensive error handling for the CMS system
 * with specific error types for different failure scenarios. The error
 * classes extend the standard Error class with additional context
 * information for better debugging and error handling.
 * 
 * The error system supports structured error handling with error codes,
 * provider information, and detailed error messages that help developers
 * identify and resolve issues quickly.
 * 
 * Key Features:
 * - Hierarchical error class structure
 * - Error codes for programmatic error handling
 * - Provider-specific error context
 * - Detailed error messages for debugging
 * - Type-safe error handling patterns
 * - Integration with logging and monitoring systems
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */

import type { ContentType } from './content'

/**
 * Error Code Enumeration
 * 
 * Defines standardized error codes for different types of
 * CMS errors. These codes enable programmatic error handling
 * and integration with monitoring systems.
 * 
 * @example
 * ```typescript
 * if (error instanceof CMSError && error.code === 'CONTENT_NOT_FOUND') {
 *   // Handle content not found specifically
 * }
 * ```
 */
export enum CMSErrorCode {
  // General errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  
  // Content errors
  CONTENT_NOT_FOUND = 'CONTENT_NOT_FOUND',
  CONTENT_INVALID = 'CONTENT_INVALID',
  CONTENT_PARSE_ERROR = 'CONTENT_PARSE_ERROR',
  
  // Provider errors
  PROVIDER_ERROR = 'PROVIDER_ERROR',
  PROVIDER_NOT_FOUND = 'PROVIDER_NOT_FOUND',
  PROVIDER_CONNECTION_ERROR = 'PROVIDER_CONNECTION_ERROR',
  PROVIDER_AUTHENTICATION_ERROR = 'PROVIDER_AUTHENTICATION_ERROR',
  
  // Cache errors
  CACHE_ERROR = 'CACHE_ERROR',
  CACHE_CONNECTION_ERROR = 'CACHE_CONNECTION_ERROR',
  CACHE_SERIALIZATION_ERROR = 'CACHE_SERIALIZATION_ERROR',
  
  // Network errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR'
}

/**
 * Base CMS Error Class
 * 
 * The base error class for all CMS-related errors. This class
 * extends the standard JavaScript Error with additional context
 * information including error codes and provider details.
 * 
 * All specific CMS errors should extend this base class to
 * maintain consistency and enable proper error handling patterns.
 * 
 * @example
 * ```typescript
 * try {
 *   await cmsOperation()
 * } catch (error) {
 *   if (error instanceof CMSError) {
 *     console.error(`CMS Error [${error.code}]: ${error.message}`)
 *     if (error.provider) {
 *       console.error(`Provider: ${error.provider}`)
 *     }
 *   }
 * }
 * ```
 */
export class CMSError extends Error {
  /** Error code for programmatic handling */
  public readonly code: string
  /** CMS provider that generated the error (if applicable) */
  public readonly provider?: string
  /** Additional context information */
  public readonly context?: Record<string, any>
  /** Timestamp when the error occurred */
  public readonly timestamp: Date

  /**
   * Creates a new CMS error instance
   * 
   * @param message - Human-readable error message
   * @param code - Error code for programmatic handling
   * @param provider - Optional CMS provider identifier
   * @param context - Optional additional context information
   * 
   * @example
   * ```typescript
   * throw new CMSError(
   *   'Failed to retrieve content',
   *   'CONTENT_NOT_FOUND',
   *   'contentlayer2',
   *   { contentType: 'blog', slug: 'missing-post' }
   * )
   * ```
   */
  constructor(
    message: string,
    code: string = CMSErrorCode.UNKNOWN_ERROR,
    provider?: string,
    context?: Record<string, any>
  ) {
    super(message)
    this.name = 'CMSError'
    this.code = code
    this.provider = provider
    this.context = context
    this.timestamp = new Date()

    // Maintain proper stack trace for V8 engines
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, CMSError)
    }
  }

  /**
   * Convert error to JSON for logging and serialization
   * 
   * @returns JSON representation of the error
   */
  toJSON(): Record<string, any> {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      provider: this.provider,
      context: this.context,
      timestamp: this.timestamp.toISOString(),
      stack: this.stack
    }
  }

  /**
   * Get a formatted error message for display
   * 
   * @returns Formatted error message with context
   */
  getFormattedMessage(): string {
    let message = `[${this.code}] ${this.message}`
    
    if (this.provider) {
      message += ` (Provider: ${this.provider})`
    }
    
    if (this.context) {
      const contextStr = Object.entries(this.context)
        .map(([key, value]) => `${key}: ${value}`)
        .join(', ')
      message += ` (Context: ${contextStr})`
    }
    
    return message
  }
}

/**
 * Content Not Found Error
 * 
 * Thrown when a requested content item cannot be found in the CMS.
 * This error includes specific information about the content type,
 * slug, and locale that was requested.
 * 
 * @example
 * ```typescript
 * const content = await provider.getContent('blog', 'missing-post', 'en')
 * if (!content) {
 *   throw new ContentNotFoundError('blog', 'missing-post', 'en')
 * }
 * ```
 */
export class ContentNotFoundError extends CMSError {
  /** Content type that was requested */
  public readonly contentType: ContentType
  /** Content slug that was requested */
  public readonly slug: string
  /** Locale that was requested */
  public readonly locale: string

  /**
   * Creates a new content not found error
   * 
   * @param type - Content type that was not found
   * @param slug - Content slug that was not found
   * @param locale - Locale that was not found
   * @param provider - Optional CMS provider identifier
   * 
   * @example
   * ```typescript
   * throw new ContentNotFoundError('blog', 'my-post', 'en', 'contentlayer2')
   * ```
   */
  constructor(
    type: ContentType,
    slug: string,
    locale: string,
    provider?: string
  ) {
    const message = `Content not found: ${type}/${locale}/${slug}`
    
    super(
      message,
      CMSErrorCode.CONTENT_NOT_FOUND,
      provider,
      { contentType: type, slug, locale }
    )
    
    this.name = 'ContentNotFoundError'
    this.contentType = type
    this.slug = slug
    this.locale = locale

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ContentNotFoundError)
    }
  }

  /**
   * Get the content identifier that was not found
   * 
   * @returns Content identifier string
   */
  getContentIdentifier(): string {
    return `${this.contentType}/${this.locale}/${this.slug}`
  }
}

/**
 * Provider Error
 * 
 * Thrown when a CMS provider encounters an error during operation.
 * This error includes provider-specific information and can wrap
 * underlying provider errors for better debugging.
 * 
 * @example
 * ```typescript
 * try {
 *   await strapiClient.getContent(id)
 * } catch (error) {
 *   throw new ProviderError(
 *     'Failed to fetch content from Strapi',
 *     'strapi',
 *     { originalError: error.message }
 *   )
 * }
 * ```
 */
export class ProviderError extends CMSError {
  /** Original error from the provider (if available) */
  public readonly originalError?: Error

  /**
   * Creates a new provider error
   * 
   * @param message - Human-readable error message
   * @param provider - CMS provider identifier
   * @param context - Optional additional context
   * @param originalError - Optional original error from provider
   * 
   * @example
   * ```typescript
   * throw new ProviderError(
   *   'API request failed',
   *   'strapi',
   *   { endpoint: '/api/posts', status: 500 },
   *   originalError
   * )
   * ```
   */
  constructor(
    message: string,
    provider: string,
    context?: Record<string, any>,
    originalError?: Error
  ) {
    super(message, CMSErrorCode.PROVIDER_ERROR, provider, context)
    
    this.name = 'ProviderError'
    this.originalError = originalError

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ProviderError)
    }
  }

  /**
   * Get the full error chain including original error
   * 
   * @returns Array of error messages in the chain
   */
  getErrorChain(): string[] {
    const chain = [this.message]
    
    if (this.originalError) {
      chain.push(this.originalError.message)
    }
    
    return chain
  }
}

/**
 * Content Validation Error
 * 
 * Thrown when content fails validation checks. This error includes
 * detailed information about validation failures to help developers
 * identify and fix content structure issues.
 * 
 * @example
 * ```typescript
 * if (!content.title) {
 *   throw new ContentValidationError(
 *     'Content title is required',
 *     'blog',
 *     'my-post',
 *     { field: 'title', rule: 'required' }
 *   )
 * }
 * ```
 */
export class ContentValidationError extends CMSError {
  /** Content type being validated */
  public readonly contentType: ContentType
  /** Content slug being validated */
  public readonly slug: string
  /** Validation failures */
  public readonly validationErrors: Array<{
    field: string
    rule: string
    message: string
  }>

  /**
   * Creates a new content validation error
   * 
   * @param message - Human-readable error message
   * @param contentType - Content type being validated
   * @param slug - Content slug being validated
   * @param validationErrors - Array of specific validation failures
   * @param provider - Optional CMS provider identifier
   */
  constructor(
    message: string,
    contentType: ContentType,
    slug: string,
    validationErrors: Array<{
      field: string
      rule: string
      message: string
    }>,
    provider?: string
  ) {
    super(
      message,
      CMSErrorCode.CONTENT_INVALID,
      provider,
      { contentType, slug, validationErrors }
    )
    
    this.name = 'ContentValidationError'
    this.contentType = contentType
    this.slug = slug
    this.validationErrors = validationErrors

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ContentValidationError)
    }
  }

  /**
   * Get formatted validation error messages
   * 
   * @returns Array of formatted validation error messages
   */
  getValidationMessages(): string[] {
    return this.validationErrors.map(
      error => `${error.field}: ${error.message} (rule: ${error.rule})`
    )
  }
}

/**
 * Cache Error
 * 
 * Thrown when cache operations fail. This error includes information
 * about the cache operation that failed and any relevant context.
 * 
 * @example
 * ```typescript
 * try {
 *   await cache.set(key, value)
 * } catch (error) {
 *   throw new CacheError(
 *     'Failed to store item in cache',
 *     'memory',
 *     { key, operation: 'set' }
 *   )
 * }
 * ```
 */
export class CacheError extends CMSError {
  /** Cache operation that failed */
  public readonly operation: string
  /** Cache key involved in the operation */
  public readonly cacheKey?: string

  /**
   * Creates a new cache error
   * 
   * @param message - Human-readable error message
   * @param provider - Cache provider identifier
   * @param operation - Cache operation that failed
   * @param cacheKey - Optional cache key involved
   * @param context - Optional additional context
   */
  constructor(
    message: string,
    provider: string,
    operation: string,
    cacheKey?: string,
    context?: Record<string, any>
  ) {
    super(
      message,
      CMSErrorCode.CACHE_ERROR,
      provider,
      { operation, cacheKey, ...context }
    )
    
    this.name = 'CacheError'
    this.operation = operation
    this.cacheKey = cacheKey

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, CacheError)
    }
  }
}

/**
 * Error Utility Functions
 */

/**
 * Check if an error is a CMS error
 * 
 * Type guard function to check if an error is an instance
 * of CMSError or one of its subclasses.
 * 
 * @param error - Error to check
 * @returns True if error is a CMS error
 * 
 * @example
 * ```typescript
 * try {
 *   await cmsOperation()
 * } catch (error) {
 *   if (isCMSError(error)) {
 *     console.log(`CMS Error: ${error.code}`)
 *   }
 * }
 * ```
 */
export function isCMSError(error: any): error is CMSError {
  return error instanceof CMSError
}

/**
 * Check if an error is a content not found error
 * 
 * @param error - Error to check
 * @returns True if error is a content not found error
 */
export function isContentNotFoundError(error: any): error is ContentNotFoundError {
  return error instanceof ContentNotFoundError
}

/**
 * Check if an error is a provider error
 * 
 * @param error - Error to check
 * @returns True if error is a provider error
 */
export function isProviderError(error: any): error is ProviderError {
  return error instanceof ProviderError
}

/**
 * Create a standardized error response for API endpoints
 * 
 * @param error - Error to format
 * @returns Standardized error response object
 */
export function formatErrorResponse(error: CMSError): {
  error: {
    code: string
    message: string
    provider?: string
    context?: Record<string, any>
    timestamp: string
  }
} {
  return {
    error: {
      code: error.code,
      message: error.message,
      provider: error.provider,
      context: error.context,
      timestamp: error.timestamp.toISOString()
    }
  }
}
