/**
 * Strapi CMS Provider (Placeholder)
 *
 * TODO: Implement when migrating to Strapi headless CMS
 * - REST/GraphQL API integration
 * - Authentication support
 * - Multi-language content
 */

import type { ContentProvider } from '../../types'

export class StrapiProvider implements ContentProvider {
  readonly name = 'strapi'
  readonly version = '1.0.0'

  // TODO: Implement all ContentProvider methods
  async getContent(..._args: any[]): Promise<any> { throw new Error('Not implemented') }
  async getContentList(..._args: any[]): Promise<any[]> { throw new Error('Not implemented') }
  async contentExists(..._args: any[]): Promise<boolean> { throw new Error('Not implemented') }
  async getContentTitle(..._args: any[]): Promise<string | null> { throw new Error('Not implemented') }
  async getContentMetadata(..._args: any[]): Promise<any> { throw new Error('Not implemented') }
  async getAvailableLanguages(..._args: any[]): Promise<any[]> { throw new Error('Not implemented') }
  async getRelatedContent(..._args: any[]): Promise<any[]> { throw new Error('Not implemented') }
  async getContentForStaticGeneration(..._args: any[]): Promise<any[]> { throw new Error('Not implemented') }
  async getAllContentSlugs(..._args: any[]): Promise<any[]> { throw new Error('Not implemented') }
}