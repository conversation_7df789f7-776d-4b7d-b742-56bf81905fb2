/**
 * Contentlayer2 Provider Implementation
 * 
 * This provider implements the CMS abstraction layer using Contentlayer2,
 * a community-maintained fork of the original Contentlayer that supports
 * Next.js 15 and React 19. It provides type-safe content management with
 * automatic TypeScript generation and multi-language support.
 * 
 * Key Features:
 * - Type-safe content queries with full TypeScript support
 * - Multi-language content management (en/zh)
 * - Automatic URL generation and metadata extraction
 * - File system-based content organization
 * - SEO optimization with structured data support
 * - Caching layer for improved performance
 */

import type {
  ContentProvider,
  ContentItem,
  ContentType,
  QueryOptions,
  ContentMetadata,
  LanguageVersion,
  BlogContent,
  ProductContent,
  CaseStudyContent,
  MDXContent
} from '../../types'

// Contentlayer2 generated content imports
// These will be available after running 'contentlayer build'
import {
  allBlogs,
  allProducts,
  allCaseStudies,
  type Blog,
  type Product,
  type CaseStudy
} from 'contentlayer2/generated'

/**
 * Contentlayer2 Provider Class
 * 
 * Implements the ContentProvider interface using Contentlayer2 as the backend.
 * This provider handles all content operations including queries, metadata
 * extraction, and language management through the unified CMS interface.
 */
export class Contentlayer2Provider implements ContentProvider {
  readonly name = 'contentlayer2'
  readonly version = '0.4.6'

  /**
   * Convert Contentlayer2 MDX to our abstract MDX type
   */
  private convertMDX(mdx: any): MDXContent {
    return {
      raw: mdx.raw || '',
      code: mdx.code || ''
    }
  }

  /**
   * Convert Contentlayer2 content to our abstract content types
   */
  private convertContent(item: Blog | Product | CaseStudy, type: ContentType): ContentItem {
    const baseContent = {
      slug: item.slug,
      title: item.title,
      lang: item.lang,
      url: item.url,
      description: item.description,
      body: this.convertMDX(item.body),
      coverImage: item.coverImage,
      authorImage: item.authorImage,
      videoUrl: item.videoUrl,
      videoThumbnail: item.videoThumbnail,
      videoDuration: item.videoDuration,
      author: item.author,
      publishedAt: item.publishedAt,
      createdAt: item._raw?.sourceFileDir || new Date().toISOString(),
      featured: item.featured || false,
      tags: item.tags || []
    }

    switch (type) {
      case 'blog':
        return { ...baseContent, type: 'blog' } as BlogContent
      case 'product':
        return {
          ...baseContent,
          type: 'product',
          icon: (item as Product).icon
        } as ProductContent
      case 'case-study':
        return { ...baseContent, type: 'case-study' } as CaseStudyContent
      default:
        throw new Error(`Unknown content type: ${type}`)
    }
  }

  /**
   * Get content collection by type
   *
   * Returns the appropriate content collection based on the content type.
   * This is an internal helper method that maps content types to their
   * corresponding Contentlayer2 generated collections.
   *
   * @param type - Content type identifier
   * @returns Array of content items for the specified type
   */
  private getCollection(type: ContentType): ContentItem[] {
    const rawCollections = {
      blog: allBlogs,
      product: allProducts,
      'case-study': allCaseStudies
    }

    const rawCollection = rawCollections[type] || []
    return rawCollection.map(item => this.convertContent(item, type))
  }

  /**
   * Get single content item
   * 
   * Retrieves a specific content item by type, slug, and language.
   * This method provides type-safe access to individual content items
   * with automatic language filtering and slug matching.
   * 
   * @param type - Content type (blog, product, case-study)
   * @param slug - Content slug identifier
   * @param locale - Language code (en, zh)
   * @returns Promise resolving to content item or null if not found
   */
  async getContent<T extends ContentItem>(
    type: ContentType, 
    slug: string, 
    locale: string
  ): Promise<T | null> {
    try {
      const collection = this.getCollection(type)
      const content = collection.find(item => 
        item.slug === slug && item.lang === locale
      )
      
      return (content as T) || null
    } catch (error) {
      console.error(`Error fetching content: ${type}/${locale}/${slug}`, error)
      return null
    }
  }

  /**
   * Get content list with filtering and sorting
   * 
   * Retrieves a filtered and sorted list of content items for a specific
   * type and language. Supports various query options including featured
   * content filtering, pagination, and custom sorting.
   * 
   * @param type - Content type to query
   * @param locale - Language code for filtering
   * @param options - Query options for filtering and sorting
   * @returns Promise resolving to array of content items
   */
  async getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options: QueryOptions = {}
  ): Promise<T[]> {
    try {
      let content = this.getCollection(type)
        .filter(item => item.lang === locale) as T[]

      // Apply featured filter
      if (options.featured !== undefined) {
        content = content.filter(item => item.featured === options.featured)
      }

      // Apply tag filter
      if (options.tags && options.tags.length > 0) {
        content = content.filter(item => 
          item.tags && item.tags.some(tag => options.tags!.includes(tag))
        )
      }

      // Apply sorting
      if (options.sortBy) {
        content.sort((a, b) => {
          const aValue = a[options.sortBy!]
          const bValue = b[options.sortBy!]
          const order = options.order === 'desc' ? -1 : 1
          
          if (typeof aValue === 'string' && typeof bValue === 'string') {
            return aValue.localeCompare(bValue) * order
          }
          
          if (aValue && bValue && typeof aValue === 'string' && typeof bValue === 'string') {
            const dateA = new Date(aValue)
            const dateB = new Date(bValue)
            if (!isNaN(dateA.getTime()) && !isNaN(dateB.getTime())) {
              return (dateA.getTime() - dateB.getTime()) * order
            }
          }
          
          return 0
        })
      }

      // Apply pagination
      if (options.limit) {
        content = content.slice(0, options.limit)
      }

      return content
    } catch (error) {
      console.error(`Error fetching content list: ${type}/${locale}`, error)
      return []
    }
  }

  /**
   * Check if content exists
   * 
   * Verifies whether a specific content item exists for the given
   * type, slug, and language combination. This is useful for
   * language switching and content availability checks.
   * 
   * @param type - Content type to check
   * @param slug - Content slug to verify
   * @param locale - Language code to check
   * @returns Promise resolving to boolean indicating existence
   */
  async contentExists(
    type: ContentType, 
    slug: string, 
    locale: string
  ): Promise<boolean> {
    const content = await this.getContent(type, slug, locale)
    return content !== null
  }

  /**
   * Get content title
   * 
   * Retrieves the title of a specific content item. This is a
   * convenience method for quick title access without fetching
   * the entire content object.
   * 
   * @param type - Content type
   * @param slug - Content slug
   * @param locale - Language code
   * @returns Promise resolving to title string or null
   */
  async getContentTitle(
    type: ContentType, 
    slug: string, 
    locale: string
  ): Promise<string | null> {
    const content = await this.getContent(type, slug, locale)
    return content?.title || null
  }

  /**
   * Get content metadata
   * 
   * Extracts metadata information from a content item including
   * reading time, word count, and other computed properties.
   * This metadata is useful for SEO and user experience features.
   * 
   * @param type - Content type
   * @param slug - Content slug
   * @param locale - Language code
   * @returns Promise resolving to content metadata
   */
  async getContentMetadata(
    type: ContentType, 
    slug: string, 
    locale: string
  ): Promise<ContentMetadata | null> {
    const content = await this.getContent(type, slug, locale)
    
    if (!content) return null

    // Calculate reading time (approximate)
    const wordsPerMinute = 200
    const wordCount = content.body.raw?.split(/\s+/).length || 0
    const readingTime = Math.ceil(wordCount / wordsPerMinute)

    return {
      wordCount,
      readingTime,
      publishedAt: content.publishedAt,
      updatedAt: content.createdAt, // Fallback to creation time
      author: content.author,
      tags: content.tags || []
    }
  }

  /**
   * Get available language versions
   * 
   * Finds all available language versions of a specific content item.
   * This is essential for implementing language switching functionality
   * and providing users with alternative language options.
   * 
   * @param type - Content type
   * @param slug - Content slug to find versions for
   * @returns Promise resolving to array of language versions
   */
  async getAvailableLanguages(
    type: ContentType, 
    slug: string
  ): Promise<LanguageVersion[]> {
    try {
      const collection = this.getCollection(type)
      const versions = collection
        .filter(item => item.slug === slug)
        .map(item => ({
          lang: item.lang,
          title: item.title,
          url: item.url,
          available: true
        }))

      return versions
    } catch (error) {
      console.error(`Error fetching language versions: ${type}/${slug}`, error)
      return []
    }
  }

  /**
   * Get related content
   * 
   * Finds content items related to the current item based on tags
   * and content type. This is useful for implementing "related posts"
   * or "you might also like" features.
   * 
   * @param type - Content type
   * @param currentSlug - Current content slug to find related items for
   * @param locale - Language code
   * @param limit - Maximum number of related items to return
   * @returns Promise resolving to array of related content items
   */
  async getRelatedContent<T extends ContentItem>(
    type: ContentType,
    currentSlug: string,
    locale: string,
    limit: number = 3
  ): Promise<T[]> {
    try {
      const current = await this.getContent(type, currentSlug, locale)
      if (!current || !current.tags) return []

      const allContent = await this.getContentList<T>(type, locale)
      
      // Find content with matching tags
      const related = allContent
        .filter(item => item.slug !== currentSlug)
        .filter(item => {
          if (!item.tags || !current.tags) return false
          return current.tags.some(tag => item.tags!.includes(tag))
        })
        .slice(0, limit)

      // If not enough related content, fill with recent content
      if (related.length < limit) {
        const recent = allContent
          .filter(item => item.slug !== currentSlug)
          .filter(item => !related.includes(item))
          .slice(0, limit - related.length)
        
        related.push(...recent)
      }

      return related
    } catch (error) {
      console.error(`Error fetching related content: ${type}/${locale}/${currentSlug}`, error)
      return []
    }
  }

  /**
   * Get content for static generation
   *
   * Retrieves all content items of a specific type for static generation.
   * This method is optimized for build-time usage and returns all content
   * regardless of language, allowing Next.js to generate static pages.
   *
   * @param type - Content type to retrieve
   * @returns Promise resolving to array of all content items
   */
  async getContentForStaticGeneration<T extends ContentItem>(
    type: ContentType
  ): Promise<T[]> {
    try {
      const collection = this.getCollection(type)
      return collection as T[]
    } catch (error) {
      console.error(`Error fetching content for static generation: ${type}`, error)
      return []
    }
  }

  /**
   * Get all content slugs for static generation
   *
   * Retrieves all slug and locale combinations for a specific content type.
   * This is used by Next.js generateStaticParams to create static routes
   * for all available content in all languages.
   *
   * @param type - Content type to get slugs for
   * @returns Promise resolving to array of slug/locale combinations
   */
  async getAllContentSlugs(
    type: ContentType
  ): Promise<Array<{ locale: string; slug: string }>> {
    try {
      const collection = this.getCollection(type)
      return collection.map(item => ({
        locale: item.lang,
        slug: item.slug
      }))
    } catch (error) {
      console.error(`Error fetching content slugs: ${type}`, error)
      return []
    }
  }
}

// Export default instance for convenience
const contentlayer2Provider = new Contentlayer2Provider()
export default contentlayer2Provider
