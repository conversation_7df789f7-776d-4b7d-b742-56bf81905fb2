/**
 * Next.js MDX Provider (Placeholder)
 *
 * TODO: Implement when needed as fallback option
 * - File system-based content loading
 * - Dynamic MDX compilation
 * - Manual frontmatter parsing
 */

import type { ContentProvider } from '../../types'

export class NextjsMDXProvider implements ContentProvider {
  readonly name = 'nextjs-mdx'
  readonly version = '1.0.0'

  // TODO: Implement all ContentProvider methods
  async getContent(..._args: any[]): Promise<any> { throw new Error('Not implemented') }
  async getContentList(..._args: any[]): Promise<any[]> { throw new Error('Not implemented') }
  async contentExists(..._args: any[]): Promise<boolean> { throw new Error('Not implemented') }
  async getContentTitle(..._args: any[]): Promise<string | null> { throw new Error('Not implemented') }
  async getContentMetadata(..._args: any[]): Promise<any> { throw new Error('Not implemented') }
  async getAvailableLanguages(..._args: any[]): Promise<any[]> { throw new Error('Not implemented') }
  async getRelatedContent(..._args: any[]): Promise<any[]> { throw new Error('Not implemented') }
  async getContentForStaticGeneration(..._args: any[]): Promise<any[]> { throw new Error('Not implemented') }
  async getAllContentSlugs(..._args: any[]): Promise<any[]> { throw new Error('Not implemented') }
}