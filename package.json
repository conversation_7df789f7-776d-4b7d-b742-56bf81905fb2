{"name": "shipany-template-one", "version": "2.6.0", "private": true, "author": "<PERSON><PERSON><PERSON>", "homepage": "https://shipany.ai", "scripts": {"dev": "cross-env NODE_NO_WARNINGS=1 next dev --turbopack", "build": "contentlayer2 build && pnpm generate:content && next build", "start": "NODE_NO_WARNINGS=1 next start", "lint": "next lint", "analyze": "ANALYZE=true pnpm build", "cf:preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "cf:deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "cf:upload": "opennextjs-cloudflare build && opennextjs-cloudflare upload", "cf:typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts", "docker:build": "docker build -f Dockerfile -t shipany-template-one:latest .", "db:generate": "npx drizzle-kit generate --config=src/db/config.ts", "db:migrate": "npx drizzle-kit migrate --config=src/db/config.ts", "db:studio": "npx drizzle-kit studio --config=src/db/config.ts", "db:push": "npx drizzle-kit push --config=src/db/config.ts", "postinstall": "fumadocs-mdx", "generate:content": "pnpm generate:sitemap && pnpm generate:rss", "generate:sitemap": "tsx scripts/generate-sitemap.ts", "generate:rss": "tsx scripts/generate-rss.ts"}, "dependencies": {"@ai-sdk/deepseek": "^0.1.11", "@ai-sdk/openai": "^1.1.13", "@ai-sdk/openai-compatible": "^0.0.17", "@ai-sdk/provider": "^1.0.8", "@ai-sdk/provider-utils": "^2.0.7", "@ai-sdk/replicate": "^0.1.10", "@aws-sdk/client-s3": "^3.842.0", "@aws-sdk/lib-storage": "^3.842.0", "@aws-sdk/s3-request-presigner": "^3.842.0", "@devnomic/marquee": "^1.0.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.10.0", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.1.3", "@next/third-parties": "^15.1.2", "@opennextjs/cloudflare": "^1.2.1", "@openpanel/nextjs": "^1.0.7", "@openrouter/ai-sdk-provider": "^0.0.6", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.1.6", "@stripe/stripe-js": "^5.4.0", "@tabler/icons-react": "^3.31.0", "@tanstack/react-table": "^8.21.3", "@types/canvas-confetti": "^1.9.0", "@types/mdx": "^2.0.13", "@uiw/react-md-editor": "^4.0.5", "ai": "^4.1.64", "aws4fetch": "^1.0.20", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "contentlayer2": "^0.4.6", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "embla-carousel-auto-scroll": "^8.5.1", "embla-carousel-fade": "^8.5.1", "embla-carousel-react": "^8.5.1", "eslint-config-next": "^15.2.3", "framer-motion": "^11.15.0", "fumadocs-core": "^15.6.3", "fumadocs-mdx": "^11.6.11", "fumadocs-ui": "^15.6.3", "google-one-tap": "^1.0.6", "highlight.js": "^11.11.0", "lucide-react": "^0.439.0", "markdown-it": "^14.1.0", "mime": "^4.0.7", "moment": "^2.30.1", "next": "15.2.3", "next-auth": "5.0.0-beta.25", "next-contentlayer2": "^0.4.6", "next-intl": "^4.1.0", "next-themes": "^0.4.4", "openai": "^4.78.1", "postgres": "^3.4.7", "react": "^19.0.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-icon-cloud": "^4.1.4", "react-icons": "^5.4.0", "react-tweet": "^3.2.1", "recharts": "^2.15.3", "replicate": "^1.0.1", "simple-flakeid": "^0.0.5", "sonner": "^1.7.1", "stripe": "^17.5.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.3", "vaul": "^1.1.2", "zod": "^3.24.1"}, "devDependencies": {"@next/bundle-analyzer": "^15.1.3", "@tailwindcss/postcss": "^4.1.4", "@types/markdown-it": "^14.1.2", "@types/node": "^20.17.10", "@types/react": "^18.3.18", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.3.5", "@types/uuid": "^10.0.0", "cross-env": "^7.0.3", "drizzle-kit": "^0.31.1", "postcss": "^8.4.49", "tailwindcss": "^4.1.4", "tsx": "^4.20.3", "typescript": "^5.7.2", "vercel": "39.1.1", "wrangler": "4.19.1"}, "pnpm": {"overrides": {"@smithy/types": "^4.3.1"}}}